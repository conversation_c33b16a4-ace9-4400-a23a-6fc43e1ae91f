import { createSlice, PayloadAction } from '@reduxjs/toolkit'

interface UIState {
  // 面板状态
  activePanel: 'canvas' | 'custom'
  sidebarVisible: boolean

  // 拖拽状态
  isDraggingComponent: boolean
  draggedComponentType: string | null

  // 网格设置
  gridVisible: boolean
  gridSize: number
  snapToGrid: boolean

  // 主题设置
  theme: 'light' | 'dark'

  // 快捷键设置
  shortcuts: Record<string, string>
}

const defaultShortcuts = {
  'moveUp': 'w',
  'moveDown': 's',
  'moveLeft': 'a',
  'moveRight': 'd',
  'new': 'ctrl+n',
  'save': 'ctrl+s',
  'copy': 'ctrl+c',
  'paste': 'ctrl+v',
  'delete': 'delete',
  'undo': 'ctrl+z',
  'redo': 'ctrl+y',
  'zoomIn': 'ctrl+=',
  'zoomOut': 'ctrl+-',
  'resetZoom': 'ctrl+0',
  'fitToContent': 'ctrl+shift+f',
  'moveUp': 'w',
  'moveDown': 's',
  'moveLeft': 'a',
  'moveRight': 'd'
}

const initialState: UIState = {
  activePanel: 'canvas',
  sidebarVisible: true,
  isDraggingComponent: false,
  draggedComponentType: null,
  gridVisible: true,
  gridSize: 20,
  snapToGrid: true,
  theme: 'light',
  shortcuts: defaultShortcuts
}

const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    // 面板切换
    setActivePanel: (state, action: PayloadAction<UIState['activePanel']>) => {
      state.activePanel = action.payload
    },

    toggleSidebar: (state) => {
      state.sidebarVisible = !state.sidebarVisible
    },

    // 拖拽状态
    setDraggingComponent: (state, action: PayloadAction<{ isDragging: boolean; componentType?: string }>) => {
      state.isDraggingComponent = action.payload.isDragging
      state.draggedComponentType = action.payload.componentType || null
    },

    // 网格设置
    toggleGrid: (state) => {
      state.gridVisible = !state.gridVisible
    },

    setGridSize: (state, action: PayloadAction<number>) => {
      state.gridSize = Math.max(10, Math.min(50, action.payload))
    },

    toggleSnapToGrid: (state) => {
      state.snapToGrid = !state.snapToGrid
    },

    // 主题切换
    setTheme: (state, action: PayloadAction<UIState['theme']>) => {
      state.theme = action.payload
    },

    // 快捷键设置
    updateShortcut: (state, action: PayloadAction<{ action: string; shortcut: string }>) => {
      state.shortcuts[action.payload.action] = action.payload.shortcut
    },

    resetShortcuts: (state) => {
      state.shortcuts = defaultShortcuts
    },

    // 通用设置
    setAutoSave: (state, action: PayloadAction<boolean>) => {
      state.autoSave = action.payload
    },

    setAutoSaveInterval: (state, action: PayloadAction<number>) => {
      state.autoSaveInterval = Math.max(1, Math.min(60, action.payload)) // 限制在1-60分钟
    }
  }
})

export const {
  setActivePanel,
  toggleSidebar,
  setDraggingComponent,
  toggleGrid,
  setGridSize,
  toggleSnapToGrid,
  setTheme,
  updateShortcut,
  resetShortcuts,
  setAutoSave,
  setAutoSaveInterval
} = uiSlice.actions

export default uiSlice.reducer
