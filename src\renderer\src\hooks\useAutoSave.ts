import { useEffect, useRef } from 'react'
import { useAppSelector } from '../store/hooks'

export const useAutoSave = (saveCallback: () => void) => {
  const { autoSave, autoSaveInterval } = useAppSelector(state => state.ui)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  const lastSaveRef = useRef<number>(Date.now())

  useEffect(() => {
    if (autoSave) {
      // 清除之前的定时器
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }

      // 设置新的定时器
      intervalRef.current = setInterval(() => {
        const now = Date.now()
        const timeSinceLastSave = now - lastSaveRef.current
        const intervalMs = autoSaveInterval * 60 * 1000 // 转换为毫秒

        if (timeSinceLastSave >= intervalMs) {
          saveCallback()
          lastSaveRef.current = now
          console.log(`🔄 自动保存执行，间隔: ${autoSaveInterval}分钟`)
        }
      }, 30000) // 每30秒检查一次

      return () => {
        if (intervalRef.current) {
          clearInterval(intervalRef.current)
        }
      }
    } else {
      // 如果关闭自动保存，清除定时器
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
        intervalRef.current = null
      }
    }
  }, [autoSave, autoSaveInterval, saveCallback])

  // 手动保存时更新最后保存时间
  const markSaved = () => {
    lastSaveRef.current = Date.now()
  }

  return { markSaved }
}
