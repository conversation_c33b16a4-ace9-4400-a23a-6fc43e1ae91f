import React, { useState, useEffect } from 'react'
import { useAppDispatch, useAppSelector } from '../store/hooks'
import { updateShortcut, resetShortcuts } from '../store/slices/uiSlice'

interface SettingsProps {
  isOpen: boolean
  onClose: () => void
}

const Settings: React.FC<SettingsProps> = ({ isOpen, onClose }) => {
  const dispatch = useAppDispatch()
  const [activeTab, setActiveTab] = useState<'shortcuts' | 'general'>('shortcuts')

  // 从 Redux store 获取快捷键配置
  const storeShortcuts = useAppSelector(state => state.ui.shortcuts)
  const [shortcuts, setShortcuts] = useState(storeShortcuts)
  const [editingKey, setEditingKey] = useState<string | null>(null)

  // 当 Redux store 中的快捷键更新时，同步到本地状态
  useEffect(() => {
    setShortcuts(storeShortcuts)
  }, [storeShortcuts])

  if (!isOpen) return null

  const handleKeyEdit = (key: string) => {
    setEditingKey(key)
  }

  const handleKeyChange = (key: string, newValue: string) => {
    const updatedShortcuts = {
      ...shortcuts,
      [key]: newValue
    }
    setShortcuts(updatedShortcuts)
    // 立即保存到 Redux store
    dispatch(updateShortcut({ action: key, shortcut: newValue }))
    setEditingKey(null)
  }

  const resetToDefaults = () => {
    dispatch(resetShortcuts())
  }

  const handleSave = () => {
    // 保存所有快捷键到 Redux store
    Object.entries(shortcuts).forEach(([key, value]) => {
      dispatch(updateShortcut({ action: key, shortcut: value }))
    })
    onClose()
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-[600px] h-[500px] flex flex-col">
        {/* 标题栏 */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-800">设置</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 text-xl"
          >
            ✕
          </button>
        </div>

        {/* 标签页 */}
        <div className="flex border-b border-gray-200">
          <button
            className={`px-4 py-2 font-medium ${
              activeTab === 'shortcuts'
                ? 'text-blue-600 border-b-2 border-blue-600'
                : 'text-gray-600 hover:text-gray-800'
            }`}
            onClick={() => setActiveTab('shortcuts')}
          >
            快捷键设置
          </button>
          <button
            className={`px-4 py-2 font-medium ${
              activeTab === 'general'
                ? 'text-blue-600 border-b-2 border-blue-600'
                : 'text-gray-600 hover:text-gray-800'
            }`}
            onClick={() => setActiveTab('general')}
          >
            通用设置
          </button>
        </div>

        {/* 内容区域 */}
        <div className="flex-1 p-4 overflow-y-auto">
          {activeTab === 'shortcuts' && (
            <div className="space-y-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-md font-medium text-gray-800">快捷键配置</h3>
                <button
                  onClick={resetToDefaults}
                  className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded"
                >
                  恢复默认
                </button>
              </div>

              <div className="space-y-3">
                {Object.entries(shortcuts).map(([key, value]) => (
                  <div key={key} className="flex items-center justify-between py-2 border-b border-gray-100">
                    <span className="text-gray-700 font-medium">
                      {key === 'moveUp' && '向上移动'}
                      {key === 'moveDown' && '向下移动'}
                      {key === 'moveLeft' && '向左移动'}
                      {key === 'moveRight' && '向右移动'}
                      {key === 'new' && '新建项目'}
                      {key === 'save' && '保存'}
                      {key === 'copy' && '复制'}
                      {key === 'paste' && '粘贴'}
                      {key === 'delete' && '删除'}
                      {key === 'undo' && '撤销'}
                      {key === 'redo' && '重做'}
                    </span>
                    <div className="flex items-center gap-2">
                      {editingKey === key ? (
                        <input
                          type="text"
                          value={value}
                          onChange={(e) => handleKeyChange(key, e.target.value)}
                          onBlur={() => setEditingKey(null)}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                              setEditingKey(null)
                            }
                          }}
                          className="px-2 py-1 border border-gray-300 rounded text-sm w-32"
                          autoFocus
                        />
                      ) : (
                        <span
                          onClick={() => handleKeyEdit(key)}
                          className="px-2 py-1 bg-gray-100 rounded text-sm cursor-pointer hover:bg-gray-200 w-32 text-center"
                        >
                          {value}
                        </span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'general' && (
            <div className="space-y-4">
              <h3 className="text-md font-medium text-gray-800">通用设置</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between py-2">
                  <span className="text-gray-700">网格大小</span>
                  <select className="px-2 py-1 border border-gray-300 rounded text-sm">
                    <option value="10">10px</option>
                    <option value="20" selected>20px</option>
                    <option value="30">30px</option>
                  </select>
                </div>
                <div className="flex items-center justify-between py-2">
                  <span className="text-gray-700">自动保存</span>
                  <input type="checkbox" className="rounded" />
                </div>
                <div className="flex items-center justify-between py-2">
                  <span className="text-gray-700">显示网格</span>
                  <input type="checkbox" className="rounded" defaultChecked />
                </div>
              </div>
            </div>
          )}
        </div>

        {/* 底部按钮 */}
        <div className="flex items-center justify-end gap-2 p-4 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-600 hover:text-gray-800"
          >
            取消
          </button>
          <button
            onClick={handleSave}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            保存
          </button>
        </div>
      </div>
    </div>
  )
}

export default Settings
