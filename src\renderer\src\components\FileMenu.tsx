import React, { useState } from 'react'
import { useAppDispatch, useAppSelector } from '../store/hooks'
import { newProject, openProject, saveProject, markAsModified } from '../store/slices/projectSlice'
import { newCircuit, loadCircuit } from '../store/slices/circuitSlice'
import { resetView, loadViewport } from '../store/slices/viewportSlice'
import { 
  saveProjectToFile, 
  loadProjectFromFile, 
  createNewProjectData, 
  confirmSave,
  hasUnsavedChanges 
} from '../utils/fileOperations'

interface FileMenuProps {
  isOpen: boolean
  onClose: () => void
}

const FileMenu: React.FC<FileMenuProps> = ({ isOpen, onClose }) => {
  const dispatch = useAppDispatch()
  const [isLoading, setIsLoading] = useState(false)
  
  // Redux 状态
  const projectState = useAppSelector(state => state.project)
  const circuitState = useAppSelector(state => state.circuit)
  const viewportState = useAppSelector(state => state.viewport)

  // 新建项目
  const handleNewProject = async () => {
    if (hasUnsavedChanges(projectState.isModified)) {
      const action = await confirmSave(projectState.projectName)
      if (action === 'cancel') return
      if (action === 'save') {
        await handleSaveProject()
      }
    }

    // 重置所有状态
    dispatch(newProject())
    dispatch(newCircuit({ name: '未命名项目' }))
    dispatch(resetView())
    
    console.log('📄 新建项目')
    onClose()
  }

  // 打开项目
  const handleOpenProject = async () => {
    if (hasUnsavedChanges(projectState.isModified)) {
      const action = await confirmSave(projectState.projectName)
      if (action === 'cancel') return
      if (action === 'save') {
        await handleSaveProject()
      }
    }

    setIsLoading(true)
    try {
      const result = await loadProjectFromFile()
      if (result) {
        const { data, fileName } = result
        
        // 加载项目数据到各个 slice
        dispatch(openProject({ 
          filePath: fileName, 
          projectName: data.projectName || fileName 
        }))
        dispatch(loadCircuit(data.circuit))
        if (data.viewport) {
          dispatch(loadViewport(data.viewport))
        }
        
        console.log('📂 项目已打开:', fileName)
      }
    } catch (error) {
      console.error('❌ 打开项目失败:', error)
      alert('打开项目失败，请检查文件格式。')
    } finally {
      setIsLoading(false)
      onClose()
    }
  }

  // 保存项目
  const handleSaveProject = async () => {
    setIsLoading(true)
    try {
      const projectData = createNewProjectData(
        circuitState,
        [], // TODO: 添加自定义元件数据
        viewportState
      )
      
      // 使用当前项目名称
      projectData.projectName = projectState.projectName
      projectData.lastModified = new Date().toISOString()
      
      const savedFileName = await saveProjectToFile(projectData, projectState.projectName)
      if (savedFileName) {
        dispatch(saveProject({ filePath: savedFileName }))
        console.log('💾 项目已保存')
      }
    } catch (error) {
      console.error('❌ 保存项目失败:', error)
      alert('保存项目失败，请重试。')
    } finally {
      setIsLoading(false)
      onClose()
    }
  }

  // 另存为
  const handleSaveAsProject = async () => {
    setIsLoading(true)
    try {
      const newName = prompt('请输入项目名称:', projectState.projectName)
      if (!newName) return

      const projectData = createNewProjectData(
        circuitState,
        [], // TODO: 添加自定义元件数据
        viewportState
      )
      
      projectData.projectName = newName
      projectData.lastModified = new Date().toISOString()
      
      const savedFileName = await saveProjectToFile(projectData, newName)
      if (savedFileName) {
        dispatch(saveProject({ filePath: savedFileName }))
        console.log('💾 项目已另存为:', newName)
      }
    } catch (error) {
      console.error('❌ 另存为失败:', error)
      alert('另存为失败，请重试。')
    } finally {
      setIsLoading(false)
      onClose()
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-[400px] flex flex-col">
        {/* 标题栏 */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-800">文件操作</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 text-xl"
          >
            ✕
          </button>
        </div>

        {/* 菜单项 */}
        <div className="p-4 space-y-2">
          <button
            onClick={handleNewProject}
            disabled={isLoading}
            className="w-full flex items-center gap-3 px-4 py-3 text-left hover:bg-gray-50 rounded-lg transition-colors disabled:opacity-50"
          >
            <span className="text-xl">📄</span>
            <div>
              <div className="font-medium text-gray-800">新建项目</div>
              <div className="text-sm text-gray-500">创建一个新的电路项目</div>
            </div>
          </button>

          <button
            onClick={handleOpenProject}
            disabled={isLoading}
            className="w-full flex items-center gap-3 px-4 py-3 text-left hover:bg-gray-50 rounded-lg transition-colors disabled:opacity-50"
          >
            <span className="text-xl">📂</span>
            <div>
              <div className="font-medium text-gray-800">打开项目</div>
              <div className="text-sm text-gray-500">从文件加载现有项目</div>
            </div>
          </button>

          <button
            onClick={handleSaveProject}
            disabled={isLoading}
            className="w-full flex items-center gap-3 px-4 py-3 text-left hover:bg-gray-50 rounded-lg transition-colors disabled:opacity-50"
          >
            <span className="text-xl">💾</span>
            <div>
              <div className="font-medium text-gray-800">保存项目</div>
              <div className="text-sm text-gray-500">保存当前项目到文件</div>
            </div>
          </button>

          <button
            onClick={handleSaveAsProject}
            disabled={isLoading}
            className="w-full flex items-center gap-3 px-4 py-3 text-left hover:bg-gray-50 rounded-lg transition-colors disabled:opacity-50"
          >
            <span className="text-xl">📋</span>
            <div>
              <div className="font-medium text-gray-800">另存为</div>
              <div className="text-sm text-gray-500">以新名称保存项目</div>
            </div>
          </button>
        </div>

        {/* 项目信息 */}
        <div className="p-4 border-t border-gray-200 bg-gray-50">
          <div className="text-sm text-gray-600">
            <div className="flex justify-between">
              <span>当前项目:</span>
              <span className="font-medium">{projectState.projectName}</span>
            </div>
            {projectState.isModified && (
              <div className="text-orange-600 mt-1">
                ⚠️ 有未保存的更改
              </div>
            )}
            {projectState.lastSaved && (
              <div className="mt-1">
                最后保存: {new Date(projectState.lastSaved).toLocaleString()}
              </div>
            )}
          </div>
        </div>

        {/* 加载状态 */}
        {isLoading && (
          <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center rounded-lg">
            <div className="text-center">
              <div className="animate-spin text-2xl mb-2">⏳</div>
              <div className="text-gray-600">处理中...</div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default FileMenu
