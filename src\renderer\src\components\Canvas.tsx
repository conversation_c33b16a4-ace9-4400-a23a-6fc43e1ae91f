import React, { useEffect, useRef, useState } from 'react'
import { fabric } from 'fabric'
import { useAppDispatch, useAppSelector } from '../store/hooks'
import { moveByWASD, setZoom, zoomBy, setDragging } from '../store/slices/viewportSlice'
import { addComponent } from '../store/slices/circuitSlice'
import { setDraggingComponent } from '../store/slices/uiSlice'
import { generateId } from '../../../shared/utils'

const Canvas: React.FC = () => {
  const dispatch = useAppDispatch()
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const fabricCanvasRef = useRef<fabric.Canvas | null>(null)
  const [isInitialized, setIsInitialized] = useState(false)
  const [viewTransform, setViewTransform] = useState({ x: 0, y: 0, scale: 1 })

  // Redux 状态
  const viewport = useAppSelector(state => state.viewport)
  const { isDraggingComponent, draggedComponentType } = useAppSelector(state => state.ui)
  const { gridVisible, gridSize, snapToGrid } = useAppSelector(state => state.ui)

  // 初始化 Fabric.js 画布
  useEffect(() => {
    console.log('🚀 Canvas useEffect - canvasRef:', !!canvasRef.current, 'isInitialized:', isInitialized)
    if (!canvasRef.current || isInitialized) return

    console.log('🎨 Creating Fabric canvas...')
    const canvas = new fabric.Canvas(canvasRef.current, {
      width: window.innerWidth - 256, // 减去侧边栏宽度
      height: window.innerHeight - 100, // 减去工具栏和状态栏高度
      backgroundColor: 'transparent',
      selection: true,
      preserveObjectStacking: true
    })

    fabricCanvasRef.current = canvas
    setIsInitialized(true)
    console.log('✅ Canvas initialized, size:', canvas.getWidth(), 'x', canvas.getHeight())

    // 绘制网格 - 使用原生 Canvas API
    const drawGrid = () => {
      console.log('🔍 Drawing grid - gridVisible:', gridVisible, 'gridSize:', gridSize)

      if (!gridVisible) {
        console.log('❌ Grid not visible, clearing grid lines')
        // 清除网格
        canvas.getObjects().forEach((obj: any) => {
          if (obj.name === 'grid-line') {
            canvas.remove(obj)
          }
        })
        canvas.renderAll()
        return
      }

      const canvasWidth = canvas.getWidth()
      const canvasHeight = canvas.getHeight()
      console.log('📐 Canvas size:', canvasWidth, 'x', canvasHeight)

      // 使用实际的显示尺寸
      const actualWidth = window.innerWidth - 256
      const actualHeight = window.innerHeight - 100
      console.log('📐 Actual display size:', actualWidth, 'x', actualHeight)

      // 清除之前的网格
      let removedCount = 0
      canvas.getObjects().forEach((obj: any) => {
        if (obj.name === 'grid-line') {
          canvas.remove(obj)
          removedCount++
        }
      })
      console.log('🗑️ Removed', removedCount, 'old grid lines')

      // 使用原生 Canvas API 绘制网格
      if (!canvasRef.current) {
        console.error('❌ Canvas ref not available')
        return
      }
      const ctx = canvasRef.current.getContext('2d')
      if (!ctx) {
        console.error('❌ Cannot get 2D context')
        return
      }
      ctx.save()
      ctx.strokeStyle = '#d1d5db'
      ctx.lineWidth = 1
      ctx.globalAlpha = 1

      console.log('🎨 Drawing with native Canvas API')

      // 绘制垂直线
      let verticalLines = 0
      for (let x = 0; x <= canvasWidth; x += gridSize) {
        ctx.beginPath()
        ctx.moveTo(x, 0)
        ctx.lineTo(x, canvasHeight)
        ctx.stroke()
        verticalLines++
      }

      // 绘制水平线
      let horizontalLines = 0
      for (let y = 0; y <= canvasHeight; y += gridSize) {
        ctx.beginPath()
        ctx.moveTo(0, y)
        ctx.lineTo(canvasWidth, y)
        ctx.stroke()
        horizontalLines++
      }

      ctx.restore()

      console.log('✅ Drew', verticalLines, 'vertical lines and', horizontalLines, 'horizontal lines with native API')
      console.log('🔄 Native canvas drawing completed')
    }

    // 初始绘制网格
    setTimeout(drawGrid, 100)

    // 监听关键事件来重绘网格
    canvas.on('after:render', () => {
      setTimeout(drawGrid, 10) // 延迟一点确保 Fabric.js 渲染完成
    })
    canvas.on('object:added', () => {
      setTimeout(drawGrid, 10)
    })
    canvas.on('object:removed', () => {
      setTimeout(drawGrid, 10)
    })

    // 定时重绘网格，确保持续显示
    const gridInterval = setInterval(() => {
      if (gridVisible) {
        drawGrid()
      }
    }, 500)

    // 处理窗口大小变化
    const handleResize = () => {
      canvas.setDimensions({
        width: window.innerWidth - 256,
        height: window.innerHeight - 100
      })
      drawGrid()
    }

    window.addEventListener('resize', handleResize)

    // 处理拖放
    const handleDragOver = (e: DragEvent) => {
      e.preventDefault()
    }

    const handleDrop = (e: DragEvent) => {
      e.preventDefault()
      if (!draggedComponentType) return

      const rect = canvas.getElement().getBoundingClientRect()
      const x = e.clientX - rect.left
      const y = e.clientY - rect.top

      // 网格吸附
      const finalX = snapToGrid ? Math.round(x / gridSize) * gridSize : x
      const finalY = snapToGrid ? Math.round(y / gridSize) * gridSize : y

      // 创建元件
      createComponent(draggedComponentType, finalX, finalY)
      dispatch(setDraggingComponent({ isDragging: false }))
    }

    canvas.getElement().addEventListener('dragover', handleDragOver)
    canvas.getElement().addEventListener('drop', handleDrop)

    return () => {
      clearInterval(gridInterval)
      window.removeEventListener('resize', handleResize)
      canvas.getElement().removeEventListener('dragover', handleDragOver)
      canvas.getElement().removeEventListener('drop', handleDrop)
      canvas.dispose()
    }
  }, [isInitialized])

  // 监听网格状态变化
  useEffect(() => {
    if (canvasRef.current && isInitialized) {
      console.log('🔄 Grid state changed, redrawing...')
      setTimeout(() => {
        const drawGrid = () => {
          console.log('🔍 Drawing grid - gridVisible:', gridVisible, 'gridSize:', gridSize)

          if (!gridVisible) {
            console.log('❌ Grid not visible, clearing grid lines')
            return
          }

          if (!canvasRef.current) {
            console.error('❌ Canvas ref not available')
            return
          }

          const canvasWidth = canvasRef.current.width
          const canvasHeight = canvasRef.current.height
          console.log('📐 Canvas size:', canvasWidth, 'x', canvasHeight)

          // 使用实际的显示尺寸
          const actualWidth = window.innerWidth - 256
          const actualHeight = window.innerHeight - 100
          console.log('📐 Actual display size:', actualWidth, 'x', actualHeight)

          // 使用原生 Canvas API 绘制网格
          const ctx = canvasRef.current.getContext('2d')
          if (!ctx) {
            console.error('❌ Cannot get 2D context')
            return
          }
          ctx.save()
          ctx.strokeStyle = '#d1d5db'
          ctx.lineWidth = 1
          ctx.globalAlpha = 1

          console.log('🎨 Drawing with native Canvas API')

          // 绘制垂直线
          let verticalLines = 0
          for (let x = 0; x <= actualWidth; x += gridSize) {
            ctx.beginPath()
            ctx.moveTo(x, 0)
            ctx.lineTo(x, actualHeight)
            ctx.stroke()
            verticalLines++
          }

          // 绘制水平线
          let horizontalLines = 0
          for (let y = 0; y <= actualHeight; y += gridSize) {
            ctx.beginPath()
            ctx.moveTo(0, y)
            ctx.lineTo(actualWidth, y)
            ctx.stroke()
            horizontalLines++
          }

          ctx.restore()

          console.log('✅ Drew', verticalLines, 'vertical lines and', horizontalLines, 'horizontal lines with native API')
          console.log('🔄 Native canvas drawing completed')
        }
        drawGrid()
      }, 100)
    }
  }, [gridVisible, gridSize, isInitialized])

  // 创建元件的函数
  const createComponent = (type: string, x: number, y: number) => {
    if (!fabricCanvasRef.current) return

    const canvas = fabricCanvasRef.current
    let fabricObject: fabric.Object

    switch (type) {
      case 'resistor':
        fabricObject = new fabric.Rect({
          left: x,
          top: y,
          width: 60,
          height: 20,
          fill: 'white',
          stroke: '#374151',
          strokeWidth: 2,
          rx: 4,
          ry: 4
        })
        break
      case 'capacitor':
        fabricObject = new fabric.Group([
          new fabric.Line([0, 0, 0, 40], { stroke: '#374151', strokeWidth: 3 }),
          new fabric.Line([10, 0, 10, 40], { stroke: '#374151', strokeWidth: 3 })
        ], {
          left: x,
          top: y
        })
        break
      case 'battery':
        fabricObject = new fabric.Group([
          new fabric.Line([0, 10, 0, 30], { stroke: '#374151', strokeWidth: 4 }),
          new fabric.Line([15, 5, 15, 35], { stroke: '#374151', strokeWidth: 2 })
        ], {
          left: x,
          top: y
        })
        break
      default:
        fabricObject = new fabric.Circle({
          left: x,
          top: y,
          radius: 20,
          fill: '#e5e7eb',
          stroke: '#374151',
          strokeWidth: 2
        })
    }

    // 添加到画布
    canvas.add(fabricObject)
    canvas.setActiveObject(fabricObject)
    canvas.renderAll()

    // 添加到 Redux store
    const component = {
      id: generateId(),
      name: type,
      type: type,
      position: { x, y },
      size: { width: 60, height: 40 },
      pins: [], // TODO: 根据元件类型添加引脚
      properties: {}
    }

    dispatch(addComponent(component))
  }

  // 键盘事件处理
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // 防止在输入框中触发
      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
        return
      }

      if (!fabricCanvasRef.current) return

      const speed = e.shiftKey ? 40 : 20 // Shift 键加速

      switch (e.key.toLowerCase()) {
        case 'w':
          e.preventDefault()
          dispatch(moveByWASD({ direction: 'w', speed }))
          setViewTransform(prev => ({ ...prev, y: prev.y + speed }))
          console.log('🔼 Moving up')
          break
        case 'a':
          e.preventDefault()
          dispatch(moveByWASD({ direction: 'a', speed }))
          setViewTransform(prev => ({ ...prev, x: prev.x + speed }))
          console.log('◀️ Moving left')
          break
        case 's':
          e.preventDefault()
          dispatch(moveByWASD({ direction: 's', speed }))
          setViewTransform(prev => ({ ...prev, y: prev.y - speed }))
          console.log('🔽 Moving down')
          break
        case 'd':
          e.preventDefault()
          dispatch(moveByWASD({ direction: 'd', speed }))
          setViewTransform(prev => ({ ...prev, x: prev.x - speed }))
          console.log('▶️ Moving right')
          break
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [dispatch, isInitialized])

  // 鼠标滚轮缩放
  useEffect(() => {
    if (!canvasRef.current) return

    const canvasElement = canvasRef.current

    const handleWheel = (e: WheelEvent) => {
      e.preventDefault()
      e.stopPropagation()

      const delta = e.deltaY > 0 ? 0.9 : 1.1
      const rect = canvasElement.getBoundingClientRect()
      const pointer = {
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
      }

      // 更新本地视图变换
      setViewTransform(prev => ({
        ...prev,
        scale: Math.max(0.1, Math.min(5, prev.scale * delta))
      }))

      // 同步到 Redux store
      dispatch(zoomBy({
        factor: delta,
        center: { x: pointer.x, y: pointer.y }
      }))

      console.log('🔍 Zoom:', delta > 1 ? 'in' : 'out', 'at', pointer.x, pointer.y)
    }

    canvasElement.addEventListener('wheel', handleWheel, { passive: false })

    return () => {
      canvasElement.removeEventListener('wheel', handleWheel)
    }
  }, [dispatch, isInitialized])

  return (
    <div className="canvas-container" style={{ backgroundColor: '#f9fafb' }}>
      <div
        style={{
          transform: `translate(${viewTransform.x}px, ${viewTransform.y}px) scale(${viewTransform.scale})`,
          transformOrigin: '0 0',
          transition: 'transform 0.1s ease-out'
        }}
      >
        <canvas
          ref={canvasRef}
          className="canvas"
          style={{
            cursor: isDraggingComponent ? 'copy' : 'default'
          }}
        />
      </div>
      
      {/* 画布信息显示 */}
      <div className="absolute top-4 right-4 bg-white bg-opacity-90 px-3 py-2 rounded-md text-sm text-gray-600">
        <div>缩放: {Math.round(viewTransform.scale * 100)}%</div>
        <div>位置: ({Math.round(viewTransform.x)}, {Math.round(viewTransform.y)})</div>
        <div>网格: {gridVisible ? '显示' : '隐藏'} ({gridSize}px)</div>
        <div className="text-xs text-gray-500 mt-1">
          WASD: 移动视图 | 滚轮: 缩放
        </div>
      </div>
    </div>
  )
}

export default Canvas
