import React, { useEffect, useRef, useState } from 'react'
import { fabric } from 'fabric'
import { useAppDispatch, useAppSelector } from '../store/hooks'
import { moveByWASD, setZoom, zoomBy, setDragging } from '../store/slices/viewportSlice'
import { addComponent } from '../store/slices/circuitSlice'
import { setDraggingComponent } from '../store/slices/uiSlice'
import { generateId } from '../../../shared/utils'

const Canvas: React.FC = () => {
  const dispatch = useAppDispatch()
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const fabricCanvasRef = useRef<fabric.Canvas | null>(null)
  const [isInitialized, setIsInitialized] = useState(false)

  // Redux 状态
  const viewport = useAppSelector(state => state.viewport)
  const { isDraggingComponent, draggedComponentType } = useAppSelector(state => state.ui)
  const { gridVisible, gridSize, snapToGrid } = useAppSelector(state => state.ui)

  // 初始化 Fabric.js 画布
  useEffect(() => {
    if (!canvasRef.current || isInitialized) return

    const canvas = new fabric.Canvas(canvasRef.current, {
      width: window.innerWidth - 256, // 减去侧边栏宽度
      height: window.innerHeight - 100, // 减去工具栏和状态栏高度
      backgroundColor: '#f9fafb',
      selection: true,
      preserveObjectStacking: true
    })

    fabricCanvasRef.current = canvas
    setIsInitialized(true)

    // 绘制网格
    const drawGrid = () => {
      console.log('Drawing grid, gridVisible:', gridVisible, 'gridSize:', gridSize)

      if (!gridVisible) {
        // 清除网格
        canvas.getObjects().forEach((obj: any) => {
          if (obj.name === 'grid-line') {
            canvas.remove(obj)
          }
        })
        canvas.renderAll()
        return
      }

      const canvasWidth = canvas.getWidth()
      const canvasHeight = canvas.getHeight()

      // 清除之前的网格
      canvas.getObjects().forEach((obj: any) => {
        if (obj.name === 'grid-line') {
          canvas.remove(obj)
        }
      })

      // 简化的网格绘制，不考虑视图变换
      // 绘制垂直线
      for (let x = 0; x <= canvasWidth; x += gridSize) {
        const line = new fabric.Line([x, 0, x, canvasHeight], {
          stroke: '#d1d5db',
          strokeWidth: 1,
          selectable: false,
          evented: false,
          name: 'grid-line',
          excludeFromExport: true,
          hoverCursor: 'default',
          moveCursor: 'default'
        })
        canvas.add(line)
        canvas.sendToBack(line)
      }

      // 绘制水平线
      for (let y = 0; y <= canvasHeight; y += gridSize) {
        const line = new fabric.Line([0, y, canvasWidth, y], {
          stroke: '#d1d5db',
          strokeWidth: 1,
          selectable: false,
          evented: false,
          name: 'grid-line',
          excludeFromExport: true,
          hoverCursor: 'default',
          moveCursor: 'default'
        })
        canvas.add(line)
        canvas.sendToBack(line)
      }

      canvas.renderAll()
    }

    drawGrid()

    // 监听关键事件来重绘网格
    canvas.on('after:render', drawGrid)
    canvas.on('object:added', drawGrid)
    canvas.on('object:removed', drawGrid)

    // 定时重绘网格，确保持续显示
    const gridInterval = setInterval(drawGrid, 200)

    // 处理窗口大小变化
    const handleResize = () => {
      canvas.setDimensions({
        width: window.innerWidth - 256,
        height: window.innerHeight - 100
      })
      drawGrid()
    }

    window.addEventListener('resize', handleResize)

    // 处理拖放
    const handleDragOver = (e: DragEvent) => {
      e.preventDefault()
    }

    const handleDrop = (e: DragEvent) => {
      e.preventDefault()
      if (!draggedComponentType) return

      const rect = canvas.getElement().getBoundingClientRect()
      const x = e.clientX - rect.left
      const y = e.clientY - rect.top

      // 网格吸附
      const finalX = snapToGrid ? Math.round(x / gridSize) * gridSize : x
      const finalY = snapToGrid ? Math.round(y / gridSize) * gridSize : y

      // 创建元件
      createComponent(draggedComponentType, finalX, finalY)
      dispatch(setDraggingComponent({ isDragging: false }))
    }

    canvas.getElement().addEventListener('dragover', handleDragOver)
    canvas.getElement().addEventListener('drop', handleDrop)

    return () => {
      clearInterval(gridInterval)
      window.removeEventListener('resize', handleResize)
      canvas.getElement().removeEventListener('dragover', handleDragOver)
      canvas.getElement().removeEventListener('drop', handleDrop)
      canvas.dispose()
    }
  }, [isInitialized, gridVisible, gridSize, snapToGrid, draggedComponentType])

  // 创建元件的函数
  const createComponent = (type: string, x: number, y: number) => {
    if (!fabricCanvasRef.current) return

    const canvas = fabricCanvasRef.current
    let fabricObject: fabric.Object

    switch (type) {
      case 'resistor':
        fabricObject = new fabric.Rect({
          left: x,
          top: y,
          width: 60,
          height: 20,
          fill: 'white',
          stroke: '#374151',
          strokeWidth: 2,
          rx: 4,
          ry: 4
        })
        break
      case 'capacitor':
        fabricObject = new fabric.Group([
          new fabric.Line([0, 0, 0, 40], { stroke: '#374151', strokeWidth: 3 }),
          new fabric.Line([10, 0, 10, 40], { stroke: '#374151', strokeWidth: 3 })
        ], {
          left: x,
          top: y
        })
        break
      case 'battery':
        fabricObject = new fabric.Group([
          new fabric.Line([0, 10, 0, 30], { stroke: '#374151', strokeWidth: 4 }),
          new fabric.Line([15, 5, 15, 35], { stroke: '#374151', strokeWidth: 2 })
        ], {
          left: x,
          top: y
        })
        break
      default:
        fabricObject = new fabric.Circle({
          left: x,
          top: y,
          radius: 20,
          fill: '#e5e7eb',
          stroke: '#374151',
          strokeWidth: 2
        })
    }

    // 添加到画布
    canvas.add(fabricObject)
    canvas.setActiveObject(fabricObject)
    canvas.renderAll()

    // 添加到 Redux store
    const component = {
      id: generateId(),
      name: type,
      type: type,
      position: { x, y },
      size: { width: 60, height: 40 },
      pins: [], // TODO: 根据元件类型添加引脚
      properties: {}
    }

    dispatch(addComponent(component))
  }

  // 键盘事件处理
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // 防止在输入框中触发
      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
        return
      }

      if (!fabricCanvasRef.current) return

      const canvas = fabricCanvasRef.current
      const speed = e.shiftKey ? 40 : 20 // Shift 键加速

      switch (e.key.toLowerCase()) {
        case 'w':
          e.preventDefault()
          dispatch(moveByWASD({ direction: 'w', speed }))
          canvas.relativePan({ x: 0, y: speed })
          break
        case 'a':
          e.preventDefault()
          dispatch(moveByWASD({ direction: 'a', speed }))
          canvas.relativePan({ x: speed, y: 0 })
          break
        case 's':
          e.preventDefault()
          dispatch(moveByWASD({ direction: 's', speed }))
          canvas.relativePan({ x: 0, y: -speed })
          break
        case 'd':
          e.preventDefault()
          dispatch(moveByWASD({ direction: 'd', speed }))
          canvas.relativePan({ x: -speed, y: 0 })
          break
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [dispatch, isInitialized])

  // 鼠标滚轮缩放
  useEffect(() => {
    if (!fabricCanvasRef.current) return

    const canvas = fabricCanvasRef.current

    const handleWheel = (opt: any) => {
      const e = opt.e
      e.preventDefault()
      e.stopPropagation()

      const delta = e.deltaY > 0 ? 0.9 : 1.1
      const pointer = canvas.getPointer(e)

      // 使用 Fabric.js 的内置缩放功能
      canvas.zoomToPoint({ x: pointer.x, y: pointer.y }, canvas.getZoom() * delta)

      // 同步到 Redux store
      dispatch(zoomBy({
        factor: delta,
        center: { x: pointer.x, y: pointer.y }
      }))
    }

    canvas.on('mouse:wheel', handleWheel)

    return () => {
      canvas.off('mouse:wheel', handleWheel)
    }
  }, [dispatch, isInitialized])

  return (
    <div className="canvas-container">
      <canvas
        ref={canvasRef}
        className="canvas"
        style={{
          cursor: isDraggingComponent ? 'copy' : 'default'
        }}
      />
      
      {/* 画布信息显示 */}
      <div className="absolute top-4 right-4 bg-white bg-opacity-90 px-3 py-2 rounded-md text-sm text-gray-600">
        <div>缩放: {fabricCanvasRef.current ? Math.round(fabricCanvasRef.current.getZoom() * 100) : Math.round(viewport.zoom * 100)}%</div>
        <div>网格: {gridVisible ? '显示' : '隐藏'} ({gridSize}px)</div>
        <div className="text-xs text-gray-500 mt-1">
          WASD: 移动视图 | 滚轮: 缩放
        </div>
      </div>
    </div>
  )
}

export default Canvas
