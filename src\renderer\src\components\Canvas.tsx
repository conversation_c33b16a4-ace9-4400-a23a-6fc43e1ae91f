import React, { useEffect, useRef, useState } from 'react'
import { fabric } from 'fabric'
import { useAppDispatch, useAppSelector } from '../store/hooks'
import { moveByWASD, setZoom, zoomBy, setDragging } from '../store/slices/viewportSlice'
import { addComponent } from '../store/slices/circuitSlice'
import { setDraggingComponent } from '../store/slices/uiSlice'
import { generateId } from '../../../shared/utils'

interface CanvasProps {
  isSettingsOpen?: boolean
}

const Canvas: React.FC<CanvasProps> = ({ isSettingsOpen = false }) => {
  const dispatch = useAppDispatch()
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const fabricCanvasRef = useRef<fabric.Canvas | null>(null)
  const [isInitialized, setIsInitialized] = useState(false)
  const [viewTransform, setViewTransform] = useState({ x: 0, y: 0, scale: 1 })

  // Redux 状态
  const viewport = useAppSelector(state => state.viewport)
  const { isDraggingComponent, draggedComponentType } = useAppSelector(state => state.ui)
  const { gridVisible, gridSize, snapToGrid } = useAppSelector(state => state.ui)

  // 初始化 Fabric.js 画布
  useEffect(() => {
    console.log('🚀 Canvas useEffect - canvasRef:', !!canvasRef.current, 'isInitialized:', isInitialized)
    if (!canvasRef.current || isInitialized) return

    console.log('🎨 Creating Fabric canvas...')
    const canvas = new fabric.Canvas(canvasRef.current, {
      width: (window.innerWidth - 256) * 4, // 扩大4倍
      height: (window.innerHeight - 100) * 4, // 扩大4倍
      backgroundColor: 'white',
      selection: true,
      preserveObjectStacking: true,
      renderOnAddRemove: false, // 禁用自动渲染，我们手动控制
      skipTargetFind: false,
      interactive: true
    })

    fabricCanvasRef.current = canvas
    setIsInitialized(true)
    console.log('✅ Canvas initialized, size:', canvas.getWidth(), 'x', canvas.getHeight())

    // 绘制网格 - 使用原生 Canvas API
    const drawGrid = () => {
      console.log('🔍 Drawing grid - gridVisible:', gridVisible, 'gridSize:', gridSize)

      if (!gridVisible) {
        console.log('❌ Grid not visible, clearing grid lines')
        // 清除网格
        canvas.getObjects().forEach((obj: any) => {
          if (obj.name === 'grid-line') {
            canvas.remove(obj)
          }
        })
        canvas.renderAll()
        return
      }

      const canvasWidth = canvas.getWidth()
      const canvasHeight = canvas.getHeight()
      console.log('📐 Canvas size:', canvasWidth, 'x', canvasHeight)

      // 使用实际的显示尺寸（4倍大小）
      const actualWidth = (window.innerWidth - 256) * 4
      const actualHeight = (window.innerHeight - 100) * 4
      console.log('📐 Actual display size:', actualWidth, 'x', actualHeight)

      // 清除之前的网格
      let removedCount = 0
      canvas.getObjects().forEach((obj: any) => {
        if (obj.name === 'grid-line') {
          canvas.remove(obj)
          removedCount++
        }
      })
      console.log('🗑️ Removed', removedCount, 'old grid lines')

      // 使用原生 Canvas API 绘制网格
      if (!canvasRef.current) {
        console.error('❌ Canvas ref not available')
        return
      }
      const ctx = canvasRef.current.getContext('2d')
      if (!ctx) {
        console.error('❌ Cannot get 2D context')
        return
      }
      ctx.save()
      ctx.strokeStyle = '#d1d5db'
      ctx.lineWidth = 1
      ctx.globalAlpha = 1

      console.log('🎨 Drawing with native Canvas API')

      // 绘制垂直线
      let verticalLines = 0
      for (let x = 0; x <= canvasWidth; x += gridSize) {
        ctx.beginPath()
        ctx.moveTo(x, 0)
        ctx.lineTo(x, canvasHeight)
        ctx.stroke()
        verticalLines++
      }

      // 绘制水平线
      let horizontalLines = 0
      for (let y = 0; y <= canvasHeight; y += gridSize) {
        ctx.beginPath()
        ctx.moveTo(0, y)
        ctx.lineTo(canvasWidth, y)
        ctx.stroke()
        horizontalLines++
      }

      ctx.restore()

      console.log('✅ Drew', verticalLines, 'vertical lines and', horizontalLines, 'horizontal lines with native API')
      console.log('🔄 Native canvas drawing completed')

      // 重新渲染元件（延迟更长时间确保 Redux 状态已更新）
      if (fabricCanvasRef.current && fabricCanvasRef.current.getObjects().length > 0) {
        setTimeout(() => renderComponentsWithNativeCanvas(), 100)
      }
    }

    // 初始绘制网格
    setTimeout(drawGrid, 100)

    // 监听关键事件来重绘网格
    canvas.on('after:render', () => {
      setTimeout(drawGrid, 10) // 延迟一点确保 Fabric.js 渲染完成
    })
    canvas.on('object:added', () => {
      setTimeout(drawGrid, 10)
    })
    canvas.on('object:removed', () => {
      setTimeout(drawGrid, 10)
    })

    // 定时重绘网格，确保持续显示
    const gridInterval = setInterval(() => {
      if (gridVisible) {
        drawGrid()
      }
    }, 500)

    // 处理窗口大小变化
    const handleResize = () => {
      canvas.setDimensions({
        width: (window.innerWidth - 256) * 4,
        height: (window.innerHeight - 100) * 4
      })
      drawGrid()
    }

    window.addEventListener('resize', handleResize)

    // 拖放事件现在通过 React 事件处理

    return () => {
      clearInterval(gridInterval)
      window.removeEventListener('resize', handleResize)
      canvas.dispose()
    }
  }, [isInitialized])

  // 监听网格状态变化
  useEffect(() => {
    if (canvasRef.current && isInitialized) {
      console.log('🔄 Grid state changed, redrawing...')
      setTimeout(() => {
        const drawGrid = () => {
          console.log('🔍 Drawing grid - gridVisible:', gridVisible, 'gridSize:', gridSize)

          if (!gridVisible) {
            console.log('❌ Grid not visible, clearing grid lines')
            return
          }

          if (!canvasRef.current) {
            console.error('❌ Canvas ref not available')
            return
          }

          const canvasWidth = canvasRef.current.width
          const canvasHeight = canvasRef.current.height
          console.log('📐 Canvas size:', canvasWidth, 'x', canvasHeight)

          // 使用实际的显示尺寸（4倍大小）
          const actualWidth = (window.innerWidth - 256) * 4
          const actualHeight = (window.innerHeight - 100) * 4
          console.log('📐 Actual display size:', actualWidth, 'x', actualHeight)

          // 使用原生 Canvas API 绘制网格
          const ctx = canvasRef.current.getContext('2d')
          if (!ctx) {
            console.error('❌ Cannot get 2D context')
            return
          }
          ctx.save()
          ctx.strokeStyle = '#d1d5db'
          ctx.lineWidth = 1
          ctx.globalAlpha = 1

          console.log('🎨 Drawing with native Canvas API')

          // 绘制垂直线
          let verticalLines = 0
          for (let x = 0; x <= actualWidth; x += gridSize) {
            ctx.beginPath()
            ctx.moveTo(x, 0)
            ctx.lineTo(x, actualHeight)
            ctx.stroke()
            verticalLines++
          }

          // 绘制水平线
          let horizontalLines = 0
          for (let y = 0; y <= actualHeight; y += gridSize) {
            ctx.beginPath()
            ctx.moveTo(0, y)
            ctx.lineTo(actualWidth, y)
            ctx.stroke()
            horizontalLines++
          }

          ctx.restore()

          console.log('✅ Drew', verticalLines, 'vertical lines and', horizontalLines, 'horizontal lines with native API')
          console.log('🔄 Native canvas drawing completed')
        }
        drawGrid()
      }, 100)
    }
  }, [gridVisible, gridSize, isInitialized])

  // Redux 状态
  const components = useAppSelector(state => state.circuit.components)
  const shortcuts = useAppSelector(state => state.ui.shortcuts)

  // 使用原生 Canvas API 渲染所有元件
  const renderComponentsWithNativeCanvas = () => {
    if (!canvasRef.current || !fabricCanvasRef.current) return

    const ctx = canvasRef.current.getContext('2d')
    if (!ctx) return

    // 清除之前的元件渲染（保留网格）
    const canvasWidth = canvasRef.current.width
    const canvasHeight = canvasRef.current.height

    // 重新绘制背景
    ctx.fillStyle = '#f9fafb'
    ctx.fillRect(0, 0, canvasWidth, canvasHeight)

    // 重新绘制网格
    if (gridVisible) {
      ctx.strokeStyle = '#d1d5db'
      ctx.lineWidth = 1

      // 绘制垂直线
      for (let x = 0; x <= canvasWidth; x += gridSize) {
        ctx.beginPath()
        ctx.moveTo(x, 0)
        ctx.lineTo(x, canvasHeight)
        ctx.stroke()
      }

      // 绘制水平线
      for (let y = 0; y <= canvasHeight; y += gridSize) {
        ctx.beginPath()
        ctx.moveTo(0, y)
        ctx.lineTo(canvasWidth, y)
        ctx.stroke()
      }
    }

    // 渲染所有 Fabric.js 对象
    const objects = fabricCanvasRef.current.getObjects()
    console.log('🎨 Rendering', objects.length, 'objects with native Canvas')
    console.log('📊 Redux components:', components.map(c => ({ type: c.type, x: c.position.x, y: c.position.y })))

    objects.forEach((obj: any, index) => {
      if (obj.name === 'grid-line') return // 跳过网格线

      ctx.save()

      // 获取对象属性
      const left = obj.left || 0
      const top = obj.top || 0
      const width = obj.width || 60
      const height = obj.height || 40

      console.log(`🔧 Rendering object ${index}:`, { type: obj.type, left, top, width, height })

      // 直接从 Fabric.js 对象获取元件类型（更可靠）
      const componentType = (obj as any).componentType || 'unknown'

      console.log(`🎨 Rendering component type: ${componentType} (from Fabric.js object)`)

      ctx.strokeStyle = '#374151'
      ctx.fillStyle = 'white'
      ctx.lineWidth = 2

      switch (componentType) {
        case 'resistor':
          // 电阻 - 锯齿形
          ctx.beginPath()
          ctx.moveTo(left, top + height/2)
          ctx.lineTo(left + 10, top + height/2)
          ctx.lineTo(left + 15, top + 5)
          ctx.lineTo(left + 25, top + height - 5)
          ctx.lineTo(left + 35, top + 5)
          ctx.lineTo(left + 45, top + height - 5)
          ctx.lineTo(left + 50, top + height/2)
          ctx.lineTo(left + 60, top + height/2)
          ctx.stroke()
          break

        case 'capacitor':
          // 电容 - 两条平行线
          ctx.beginPath()
          ctx.moveTo(left, top + height/2)
          ctx.lineTo(left + 25, top + height/2)
          ctx.moveTo(left + 25, top + 5)
          ctx.lineTo(left + 25, top + height - 5)
          ctx.moveTo(left + 35, top + 5)
          ctx.lineTo(left + 35, top + height - 5)
          ctx.moveTo(left + 35, top + height/2)
          ctx.lineTo(left + 60, top + height/2)
          ctx.stroke()
          break

        case 'inductor':
          // 电感 - 弧形线圈
          ctx.beginPath()
          ctx.moveTo(left, top + height/2)
          ctx.lineTo(left + 10, top + height/2)
          for (let i = 0; i < 4; i++) {
            ctx.arc(left + 15 + i * 10, top + height/2, 5, Math.PI, 0, false)
          }
          ctx.lineTo(left + 60, top + height/2)
          ctx.stroke()
          break

        case 'diode':
          // 二极管 - 三角形 + 线
          ctx.beginPath()
          ctx.moveTo(left, top + height/2)
          ctx.lineTo(left + 20, top + height/2)
          ctx.moveTo(left + 20, top + 5)
          ctx.lineTo(left + 40, top + height/2)
          ctx.lineTo(left + 20, top + height - 5)
          ctx.closePath()
          ctx.fill()
          ctx.stroke()
          ctx.beginPath()
          ctx.moveTo(left + 40, top + 5)
          ctx.lineTo(left + 40, top + height - 5)
          ctx.moveTo(left + 40, top + height/2)
          ctx.lineTo(left + 60, top + height/2)
          ctx.stroke()
          break

        case 'transistor':
          // 三极管 - 圆圈 + 线
          ctx.beginPath()
          ctx.arc(left + 30, top + height/2, 15, 0, 2 * Math.PI)
          ctx.stroke()
          // 基极
          ctx.beginPath()
          ctx.moveTo(left, top + height/2)
          ctx.lineTo(left + 15, top + height/2)
          ctx.stroke()
          // 集电极
          ctx.beginPath()
          ctx.moveTo(left + 25, top + 8)
          ctx.lineTo(left + 45, top - 5)
          ctx.stroke()
          // 发射极
          ctx.beginPath()
          ctx.moveTo(left + 25, top + height - 8)
          ctx.lineTo(left + 45, top + height + 5)
          ctx.stroke()
          break

        case 'switch':
          // 开关
          ctx.beginPath()
          ctx.moveTo(left, top + height/2)
          ctx.lineTo(left + 15, top + height/2)
          ctx.lineTo(left + 45, top + 5) // 开关臂
          ctx.moveTo(left + 45, top + height/2)
          ctx.lineTo(left + 60, top + height/2)
          ctx.stroke()
          // 开关点
          ctx.beginPath()
          ctx.arc(left + 15, top + height/2, 2, 0, 2 * Math.PI)
          ctx.arc(left + 45, top + height/2, 2, 0, 2 * Math.PI)
          ctx.fill()
          break

        case 'battery':
          // 电池 - 长短线
          ctx.beginPath()
          ctx.moveTo(left, top + height/2)
          ctx.lineTo(left + 20, top + height/2)
          // 负极（短线）
          ctx.moveTo(left + 20, top + 10)
          ctx.lineTo(left + 20, top + height - 10)
          // 正极（长线）
          ctx.moveTo(left + 30, top + 5)
          ctx.lineTo(left + 30, top + height - 5)
          ctx.moveTo(left + 30, top + height/2)
          ctx.lineTo(left + 50, top + height/2)
          ctx.stroke()
          break

        case 'ground':
          // 接地
          ctx.beginPath()
          ctx.moveTo(left + 30, top)
          ctx.lineTo(left + 30, top + 15)
          // 接地符号
          ctx.moveTo(left + 15, top + 15)
          ctx.lineTo(left + 45, top + 15)
          ctx.moveTo(left + 20, top + 20)
          ctx.lineTo(left + 40, top + 20)
          ctx.moveTo(left + 25, top + 25)
          ctx.lineTo(left + 35, top + 25)
          ctx.stroke()
          break

        default:
          // 未知元件 - 简单矩形
          ctx.fillRect(left, top, width, height)
          ctx.strokeRect(left, top, width, height)
          ctx.fillStyle = '#374151'
          ctx.font = '12px Arial'
          ctx.textAlign = 'center'
          ctx.fillText('?', left + width/2, top + height/2 + 4)
      }

      ctx.restore()
    })

    console.log('✅ Native Canvas rendering completed')
  }

  // 创建元件的函数
  const createComponent = (type: string, x: number, y: number) => {
    console.log('🔧 Creating component:', { type, x, y })

    if (!fabricCanvasRef.current) {
      console.error('❌ Fabric canvas not available')
      return
    }

    const canvas = fabricCanvasRef.current
    let fabricObject: fabric.Object

    switch (type) {
      case 'resistor':
        fabricObject = new fabric.Rect({
          left: x,
          top: y,
          width: 60,
          height: 20,
          fill: 'white',
          stroke: '#374151',
          strokeWidth: 2,
          rx: 4,
          ry: 4
        })
        break
      case 'capacitor':
        fabricObject = new fabric.Group([
          new fabric.Line([0, 0, 0, 40], { stroke: '#374151', strokeWidth: 3 }),
          new fabric.Line([10, 0, 10, 40], { stroke: '#374151', strokeWidth: 3 })
        ], {
          left: x,
          top: y
        })
        break
      case 'battery':
        fabricObject = new fabric.Group([
          new fabric.Line([0, 10, 0, 30], { stroke: '#374151', strokeWidth: 4 }),
          new fabric.Line([15, 5, 15, 35], { stroke: '#374151', strokeWidth: 2 })
        ], {
          left: x,
          top: y
        })
        break
      default:
        fabricObject = new fabric.Circle({
          left: x,
          top: y,
          radius: 20,
          fill: '#e5e7eb',
          stroke: '#374151',
          strokeWidth: 2
        })
    }

    // 将元件类型信息直接存储到 Fabric.js 对象上
    ;(fabricObject as any).componentType = type
    ;(fabricObject as any).componentId = generateId()

    // 添加到画布
    console.log('➕ Adding object to canvas')
    canvas.add(fabricObject)
    console.log('✅ Object added to Fabric.js canvas')

    // 尝试设置为活动对象
    try {
      canvas.setActiveObject(fabricObject)
      console.log('✅ Object set as active')
    } catch (error) {
      console.warn('⚠️ Could not set active object:', error)
    }

    // 使用原生 Canvas API 渲染元件
    renderComponentsWithNativeCanvas()
    console.log('✅ Component rendered with native Canvas API')

    console.log('✅ Object added, total objects:', canvas.getObjects().length)

    // 添加到 Redux store
    const component = {
      id: generateId(),
      name: type,
      type: type,
      position: { x, y },
      size: { width: 60, height: 40 },
      pins: [], // TODO: 根据元件类型添加引脚
      properties: {}
    }

    dispatch(addComponent(component))
    console.log('✅ Component added to Redux store:', component)
  }

  // 键盘事件处理
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // 防止在输入框中触发或设置打开时触发
      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement || isSettingsOpen) {
        return
      }

      if (!fabricCanvasRef.current) return

      const speed = e.shiftKey ? 40 : 20 // Shift 键加速

      // 构建当前按键字符串
      const modifiers = []
      if (e.ctrlKey) modifiers.push('Ctrl')
      if (e.altKey) modifiers.push('Alt')
      if (e.shiftKey) modifiers.push('Shift')
      if (e.metaKey) modifiers.push('Meta')

      let mainKey = e.key
      if (e.key.length === 1) mainKey = e.key.toUpperCase()

      const currentKeyString = modifiers.length > 0
        ? modifiers.join('+') + '+' + mainKey
        : mainKey

      // 获取当前快捷键配置并转换为大写进行比较
      const moveUpKey = (shortcuts.moveUp || 'w').toUpperCase()
      const moveDownKey = (shortcuts.moveDown || 's').toUpperCase()
      const moveLeftKey = (shortcuts.moveLeft || 'a').toUpperCase()
      const moveRightKey = (shortcuts.moveRight || 'd').toUpperCase()

      console.log('🎹 Key pressed:', currentKeyString, 'Checking against:', { moveUpKey, moveDownKey, moveLeftKey, moveRightKey })

      if (currentKeyString === moveUpKey) {
        e.preventDefault()
        dispatch(moveByWASD({ direction: 'w', speed }))
        setViewTransform(prev => ({ ...prev, y: prev.y + speed }))
        console.log('🔼 Moving up with key:', currentKeyString)
      } else if (currentKeyString === moveLeftKey) {
        e.preventDefault()
        dispatch(moveByWASD({ direction: 'a', speed }))
        setViewTransform(prev => ({ ...prev, x: prev.x + speed }))
        console.log('◀️ Moving left with key:', currentKeyString)
      } else if (currentKeyString === moveDownKey) {
        e.preventDefault()
        dispatch(moveByWASD({ direction: 's', speed }))
        setViewTransform(prev => ({ ...prev, y: prev.y - speed }))
        console.log('🔽 Moving down with key:', currentKeyString)
      } else if (currentKeyString === moveRightKey) {
        e.preventDefault()
        dispatch(moveByWASD({ direction: 'd', speed }))
        setViewTransform(prev => ({ ...prev, x: prev.x - speed }))
        console.log('▶️ Moving right with key:', currentKeyString)
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [dispatch, isInitialized, isSettingsOpen, shortcuts])

  // 鼠标滚轮缩放
  useEffect(() => {
    if (!canvasRef.current) return

    const canvasElement = canvasRef.current

    const handleWheel = (e: WheelEvent) => {
      e.preventDefault()
      e.stopPropagation()

      const delta = e.deltaY > 0 ? 0.9 : 1.1
      const rect = canvasElement.getBoundingClientRect()
      const pointer = {
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
      }

      // 更新本地视图变换
      setViewTransform(prev => ({
        ...prev,
        scale: Math.max(0.1, Math.min(5, prev.scale * delta))
      }))

      // 同步到 Redux store
      dispatch(zoomBy({
        factor: delta,
        center: { x: pointer.x, y: pointer.y }
      }))

      console.log('🔍 Zoom:', delta > 1 ? 'in' : 'out', 'at', pointer.x, pointer.y)
    }

    canvasElement.addEventListener('wheel', handleWheel, { passive: false })

    return () => {
      canvasElement.removeEventListener('wheel', handleWheel)
    }
  }, [dispatch, isInitialized])

  // 拖放处理函数
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    console.log('🎯 Drag over canvas container')
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    console.log('🎯 Drop on canvas container, draggedComponentType:', draggedComponentType)

    if (!draggedComponentType) {
      console.warn('❌ No dragged component type')
      return
    }

    // 获取容器的位置
    const rect = e.currentTarget.getBoundingClientRect()
    const x = e.clientX - rect.left
    const y = e.clientY - rect.top

    console.log('📍 Drop position:', { x, y, clientX: e.clientX, clientY: e.clientY, rect })

    // 考虑视图变换，计算实际的画布坐标
    const actualX = (x - viewTransform.x) / viewTransform.scale
    const actualY = (y - viewTransform.y) / viewTransform.scale

    // 网格吸附
    const finalX = snapToGrid ? Math.round(actualX / gridSize) * gridSize : actualX
    const finalY = snapToGrid ? Math.round(actualY / gridSize) * gridSize : actualY

    console.log('📍 Final position:', { finalX, finalY, actualX, actualY })

    // 创建元件
    createComponent(draggedComponentType, finalX, finalY)
    dispatch(setDraggingComponent({ isDragging: false }))
  }

  return (
    <div
      className="canvas-container"
      style={{ backgroundColor: '#f9fafb' }}
      onDragOver={handleDragOver}
      onDrop={handleDrop}
    >
      <div
        style={{
          transform: `translate(${viewTransform.x}px, ${viewTransform.y}px) scale(${viewTransform.scale})`,
          transformOrigin: '0 0',
          transition: 'transform 0.1s ease-out'
        }}
      >
        <canvas
          ref={canvasRef}
          className="canvas"
          style={{
            cursor: isDraggingComponent ? 'copy' : 'default'
          }}
        />
      </div>
      
      {/* 画布信息显示 */}
      <div className="absolute top-4 right-4 bg-white bg-opacity-90 px-3 py-2 rounded-md text-sm text-gray-600">
        <div>缩放: {Math.round(viewTransform.scale * 100)}%</div>
        <div>位置: ({Math.round(viewTransform.x)}, {Math.round(viewTransform.y)})</div>
        <div>网格: {gridVisible ? '显示' : '隐藏'} ({gridSize}px)</div>
        <div className="text-xs text-gray-500 mt-1">
          WASD: 移动视图 | 滚轮: 缩放
        </div>
      </div>
    </div>
  )
}

export default Canvas
