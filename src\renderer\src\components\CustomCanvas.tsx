import React, { useEffect, useRef, useState } from 'react'
import { fabric } from 'fabric'
import { useAppDispatch, useAppSelector } from '../store/hooks'
import { moveByWASD, setZoom, zoomBy, setDragging } from '../store/slices/viewportSlice'
import { setDraggingComponent } from '../store/slices/uiSlice'

const CustomCanvas: React.FC = () => {
  const dispatch = useAppDispatch()
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const fabricCanvasRef = useRef<fabric.Canvas | null>(null)
  const [isInitialized, setIsInitialized] = useState(false)

  // Redux 状态
  const viewport = useAppSelector(state => state.viewport)
  const { isDraggingComponent, draggedComponentType } = useAppSelector(state => state.ui)
  const { gridVisible, gridSize, snapToGrid } = useAppSelector(state => state.ui)

  // 初始化 Fabric.js 画布
  useEffect(() => {
    if (!canvasRef.current || isInitialized) return

    const canvas = new fabric.Canvas(canvasRef.current, {
      width: window.innerWidth - 256, // 减去侧边栏宽度
      height: window.innerHeight - 100, // 减去工具栏和状态栏高度
      backgroundColor: '#f9fafb',
      selection: true,
      preserveObjectStacking: true
    })

    fabricCanvasRef.current = canvas
    setIsInitialized(true)

    // 绘制网格
    const drawGrid = () => {
      if (!gridVisible) {
        // 如果网格不可见，清除所有网格线
        canvas.getObjects().forEach((obj: any) => {
          if (obj.name === 'grid-line') {
            canvas.remove(obj)
          }
        })
        canvas.renderAll()
        return
      }

      const canvasWidth = canvas.getWidth()
      const canvasHeight = canvas.getHeight()
      
      // 清除之前的网格
      canvas.getObjects().forEach((obj: any) => {
        if (obj.name === 'grid-line') {
          canvas.remove(obj)
        }
      })

      // 计算网格范围，考虑视图变换
      const vt = canvas.viewportTransform || [1, 0, 0, 1, 0, 0]
      const zoom = vt[0]
      const offsetX = -vt[4] / zoom
      const offsetY = -vt[5] / zoom
      
      // 扩大绘制范围，确保网格覆盖整个可视区域
      const padding = gridSize * 2
      const startX = Math.floor((offsetX - padding) / gridSize) * gridSize
      const startY = Math.floor((offsetY - padding) / gridSize) * gridSize
      const endX = offsetX + canvasWidth / zoom + padding
      const endY = offsetY + canvasHeight / zoom + padding

      // 绘制垂直线
      for (let x = startX; x <= endX; x += gridSize) {
        const line = new fabric.Line([x, startY, x, endY], {
          stroke: '#d1d5db',
          strokeWidth: 1,
          selectable: false,
          evented: false,
          name: 'grid-line',
          excludeFromExport: true,
          hoverCursor: 'default',
          moveCursor: 'default'
        })
        canvas.add(line)
        canvas.sendToBack(line)
      }

      // 绘制水平线
      for (let y = startY; y <= endY; y += gridSize) {
        const line = new fabric.Line([startX, y, endX, y], {
          stroke: '#d1d5db',
          strokeWidth: 1,
          selectable: false,
          evented: false,
          name: 'grid-line',
          excludeFromExport: true,
          hoverCursor: 'default',
          moveCursor: 'default'
        })
        canvas.add(line)
        canvas.sendToBack(line)
      }

      canvas.renderAll()
    }

    drawGrid()

    // 监听关键事件来重绘网格
    canvas.on('after:render', drawGrid)
    canvas.on('object:added', drawGrid)
    canvas.on('object:removed', drawGrid)

    // 定时重绘网格，确保持续显示
    const gridInterval = setInterval(drawGrid, 200)

    // 处理窗口大小变化
    const handleResize = () => {
      canvas.setDimensions({
        width: window.innerWidth - 256,
        height: window.innerHeight - 100
      })
      drawGrid()
    }

    window.addEventListener('resize', handleResize)

    // 处理拖放
    const handleDragOver = (e: DragEvent) => {
      e.preventDefault()
    }

    const handleDrop = (e: DragEvent) => {
      e.preventDefault()
      if (!draggedComponentType) return

      const rect = canvas.getElement().getBoundingClientRect()
      const x = e.clientX - rect.left
      const y = e.clientY - rect.top

      // 网格吸附
      const finalX = snapToGrid ? Math.round(x / gridSize) * gridSize : x
      const finalY = snapToGrid ? Math.round(y / gridSize) * gridSize : y

      // 创建几何图形
      createShape(draggedComponentType, finalX, finalY)
      dispatch(setDraggingComponent({ isDragging: false }))
    }

    canvas.getElement().addEventListener('dragover', handleDragOver)
    canvas.getElement().addEventListener('drop', handleDrop)

    return () => {
      clearInterval(gridInterval)
      window.removeEventListener('resize', handleResize)
      canvas.getElement().removeEventListener('dragover', handleDragOver)
      canvas.getElement().removeEventListener('drop', handleDrop)
      canvas.dispose()
    }
  }, [isInitialized, gridVisible, gridSize, snapToGrid, draggedComponentType])

  // 创建几何图形的函数
  const createShape = (type: string, x: number, y: number) => {
    if (!fabricCanvasRef.current) return

    const canvas = fabricCanvasRef.current
    let fabricObject: fabric.Object

    switch (type) {
      case 'rectangle':
        fabricObject = new fabric.Rect({
          left: x,
          top: y,
          width: 80,
          height: 60,
          fill: 'transparent',
          stroke: '#374151',
          strokeWidth: 2
        })
        break
      case 'circle':
        fabricObject = new fabric.Circle({
          left: x,
          top: y,
          radius: 30,
          fill: 'transparent',
          stroke: '#374151',
          strokeWidth: 2
        })
        break
      case 'triangle':
        fabricObject = new fabric.Triangle({
          left: x,
          top: y,
          width: 60,
          height: 60,
          fill: 'transparent',
          stroke: '#374151',
          strokeWidth: 2
        })
        break
      case 'line':
        fabricObject = new fabric.Line([x, y, x + 80, y], {
          stroke: '#374151',
          strokeWidth: 2
        })
        break
      case 'pin':
        fabricObject = new fabric.Circle({
          left: x,
          top: y,
          radius: 8,
          fill: '#ef4444',
          stroke: '#dc2626',
          strokeWidth: 2
        })
        break
      default:
        fabricObject = new fabric.Rect({
          left: x,
          top: y,
          width: 40,
          height: 40,
          fill: '#e5e7eb',
          stroke: '#374151',
          strokeWidth: 2
        })
    }

    // 添加到画布
    canvas.add(fabricObject)
    canvas.setActiveObject(fabricObject)
    canvas.renderAll()
  }

  // 键盘事件处理
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // 防止在输入框中触发
      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
        return
      }

      if (!fabricCanvasRef.current) return

      const canvas = fabricCanvasRef.current
      const speed = e.shiftKey ? 40 : 20 // Shift 键加速

      switch (e.key.toLowerCase()) {
        case 'w':
          e.preventDefault()
          dispatch(moveByWASD({ direction: 'w', speed }))
          canvas.relativePan({ x: 0, y: speed })
          break
        case 'a':
          e.preventDefault()
          dispatch(moveByWASD({ direction: 'a', speed }))
          canvas.relativePan({ x: speed, y: 0 })
          break
        case 's':
          e.preventDefault()
          dispatch(moveByWASD({ direction: 's', speed }))
          canvas.relativePan({ x: 0, y: -speed })
          break
        case 'd':
          e.preventDefault()
          dispatch(moveByWASD({ direction: 'd', speed }))
          canvas.relativePan({ x: -speed, y: 0 })
          break
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [dispatch, isInitialized])

  // 鼠标滚轮缩放
  useEffect(() => {
    if (!fabricCanvasRef.current) return

    const canvas = fabricCanvasRef.current
    
    const handleWheel = (opt: any) => {
      const e = opt.e
      e.preventDefault()
      e.stopPropagation()
      
      const delta = e.deltaY > 0 ? 0.9 : 1.1
      const pointer = canvas.getPointer(e)
      
      // 使用 Fabric.js 的内置缩放功能
      canvas.zoomToPoint({ x: pointer.x, y: pointer.y }, canvas.getZoom() * delta)
      
      // 同步到 Redux store
      dispatch(zoomBy({ 
        factor: delta, 
        center: { x: pointer.x, y: pointer.y } 
      }))
    }

    canvas.on('mouse:wheel', handleWheel)
    
    return () => {
      canvas.off('mouse:wheel', handleWheel)
    }
  }, [dispatch, isInitialized])

  return (
    <div className="canvas-container">
      <canvas
        ref={canvasRef}
        className="canvas"
        style={{
          cursor: isDraggingComponent ? 'copy' : 'default'
        }}
      />
      
      {/* 画布信息显示 */}
      <div className="absolute top-4 right-4 bg-white bg-opacity-90 px-3 py-2 rounded-md text-sm text-gray-600">
        <div>缩放: {fabricCanvasRef.current ? Math.round(fabricCanvasRef.current.getZoom() * 100) : Math.round(viewport.zoom * 100)}%</div>
        <div>网格: {gridVisible ? '显示' : '隐藏'} ({gridSize}px)</div>
        <div className="text-xs text-gray-500 mt-1">
          自定义元件设计模式
        </div>
      </div>
    </div>
  )
}

export default CustomCanvas
