import React, { useEffect, useRef, useState } from 'react'
import { fabric } from 'fabric'
import { useAppDispatch, useAppSelector } from '../store/hooks'
import { moveByWASD, setZoom, zoomBy, setDragging } from '../store/slices/viewportSlice'
import { setDraggingComponent } from '../store/slices/uiSlice'

interface CustomCanvasProps {
  isSettingsOpen?: boolean
}

const CustomCanvas: React.FC<CustomCanvasProps> = ({ isSettingsOpen = false }) => {
  const dispatch = useAppDispatch()
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const fabricCanvasRef = useRef<fabric.Canvas | null>(null)
  const [isInitialized, setIsInitialized] = useState(false)
  const [viewTransform, setViewTransform] = useState({ x: 0, y: 0, scale: 1 })

  // Redux 状态
  const shortcuts = useAppSelector(state => state.ui.shortcuts)

  // Redux 状态
  const viewport = useAppSelector(state => state.viewport)
  const { isDraggingComponent, draggedComponentType } = useAppSelector(state => state.ui)
  const { gridVisible, gridSize, snapToGrid } = useAppSelector(state => state.ui)

  // 初始化 Fabric.js 画布
  useEffect(() => {
    if (!canvasRef.current || isInitialized) return

    const canvas = new fabric.Canvas(canvasRef.current, {
      width: (window.innerWidth - 256) * 4, // 扩大4倍
      height: (window.innerHeight - 100) * 4, // 扩大4倍
      backgroundColor: 'white',
      selection: true,
      preserveObjectStacking: true,
      renderOnAddRemove: false, // 禁用自动渲染，我们手动控制
      skipTargetFind: false,
      interactive: true
    })

    fabricCanvasRef.current = canvas
    setIsInitialized(true)

    // 绘制网格 - 使用原生 Canvas API
    const drawGrid = () => {
      console.log('🔍 CustomCanvas Drawing grid - gridVisible:', gridVisible, 'gridSize:', gridSize)

      if (!gridVisible) {
        console.log('❌ CustomCanvas Grid not visible')
        return
      }

      if (!canvasRef.current) {
        console.error('❌ CustomCanvas Canvas ref not available')
        return
      }

      // 使用实际的显示尺寸（4倍大小）
      const actualWidth = (window.innerWidth - 256) * 4
      const actualHeight = (window.innerHeight - 100) * 4
      console.log('📐 CustomCanvas Actual display size:', actualWidth, 'x', actualHeight)

      // 使用原生 Canvas API 绘制网格
      const ctx = canvasRef.current.getContext('2d')
      if (!ctx) {
        console.error('❌ CustomCanvas Cannot get 2D context')
        return
      }
      ctx.save()
      ctx.strokeStyle = '#d1d5db'
      ctx.lineWidth = 1
      ctx.globalAlpha = 1

      console.log('🎨 CustomCanvas Drawing with native Canvas API')

      // 绘制垂直线
      let verticalLines = 0
      for (let x = 0; x <= actualWidth; x += gridSize) {
        ctx.beginPath()
        ctx.moveTo(x, 0)
        ctx.lineTo(x, actualHeight)
        ctx.stroke()
        verticalLines++
      }

      // 绘制水平线
      let horizontalLines = 0
      for (let y = 0; y <= actualHeight; y += gridSize) {
        ctx.beginPath()
        ctx.moveTo(0, y)
        ctx.lineTo(actualWidth, y)
        ctx.stroke()
        horizontalLines++
      }

      ctx.restore()

      console.log('✅ CustomCanvas Drew', verticalLines, 'vertical lines and', horizontalLines, 'horizontal lines with native API')
      console.log('🔄 CustomCanvas Native canvas drawing completed')
    }

    // 初始绘制网格
    setTimeout(drawGrid, 100)

    // 监听关键事件来重绘网格
    canvas.on('after:render', () => {
      setTimeout(drawGrid, 10)
    })
    canvas.on('object:added', () => {
      setTimeout(drawGrid, 10)
    })
    canvas.on('object:removed', () => {
      setTimeout(drawGrid, 10)
    })

    // 定时重绘网格，确保持续显示
    const gridInterval = setInterval(() => {
      if (gridVisible) {
        drawGrid()
      }
    }, 500)

    // 处理窗口大小变化
    const handleResize = () => {
      canvas.setDimensions({
        width: (window.innerWidth - 256) * 4,
        height: (window.innerHeight - 100) * 4
      })
      drawGrid()
    }

    window.addEventListener('resize', handleResize)

    // 拖放事件现在通过 React 事件处理

    return () => {
      clearInterval(gridInterval)
      window.removeEventListener('resize', handleResize)
      canvas.dispose()
    }
  }, [isInitialized])

  // 监听网格状态变化
  useEffect(() => {
    if (canvasRef.current && isInitialized) {
      console.log('🔄 CustomCanvas Grid state changed, redrawing...')
      setTimeout(() => {
        const drawGrid = () => {
          console.log('🔍 CustomCanvas Drawing grid - gridVisible:', gridVisible, 'gridSize:', gridSize)

          if (!gridVisible) {
            console.log('❌ CustomCanvas Grid not visible')
            return
          }

          if (!canvasRef.current) {
            console.error('❌ CustomCanvas Canvas ref not available')
            return
          }

          // 使用实际的显示尺寸（4倍大小）
          const actualWidth = (window.innerWidth - 256) * 4
          const actualHeight = (window.innerHeight - 100) * 4
          console.log('📐 CustomCanvas Actual display size:', actualWidth, 'x', actualHeight)

          // 使用原生 Canvas API 绘制网格
          const ctx = canvasRef.current.getContext('2d')
          if (!ctx) {
            console.error('❌ CustomCanvas Cannot get 2D context')
            return
          }
          ctx.save()
          ctx.strokeStyle = '#d1d5db'
          ctx.lineWidth = 1
          ctx.globalAlpha = 1

          console.log('🎨 CustomCanvas Drawing with native Canvas API')

          // 绘制垂直线
          let verticalLines = 0
          for (let x = 0; x <= actualWidth; x += gridSize) {
            ctx.beginPath()
            ctx.moveTo(x, 0)
            ctx.lineTo(x, actualHeight)
            ctx.stroke()
            verticalLines++
          }

          // 绘制水平线
          let horizontalLines = 0
          for (let y = 0; y <= actualHeight; y += gridSize) {
            ctx.beginPath()
            ctx.moveTo(0, y)
            ctx.lineTo(actualWidth, y)
            ctx.stroke()
            horizontalLines++
          }

          ctx.restore()

          console.log('✅ CustomCanvas Drew', verticalLines, 'vertical lines and', horizontalLines, 'horizontal lines with native API')
          console.log('🔄 CustomCanvas Native canvas drawing completed')
        }
        drawGrid()
      }, 100)
    }
  }, [gridVisible, gridSize, isInitialized])

  // 创建几何图形的函数
  const createShape = (type: string, x: number, y: number) => {
    if (!fabricCanvasRef.current) return

    const canvas = fabricCanvasRef.current
    let fabricObject: fabric.Object

    switch (type) {
      case 'rectangle':
        fabricObject = new fabric.Rect({
          left: x,
          top: y,
          width: 80,
          height: 60,
          fill: 'transparent',
          stroke: '#374151',
          strokeWidth: 2
        })
        break
      case 'circle':
        fabricObject = new fabric.Circle({
          left: x,
          top: y,
          radius: 30,
          fill: 'transparent',
          stroke: '#374151',
          strokeWidth: 2
        })
        break
      case 'triangle':
        fabricObject = new fabric.Triangle({
          left: x,
          top: y,
          width: 60,
          height: 60,
          fill: 'transparent',
          stroke: '#374151',
          strokeWidth: 2
        })
        break
      case 'line':
        fabricObject = new fabric.Line([x, y, x + 80, y], {
          stroke: '#374151',
          strokeWidth: 2
        })
        break
      case 'pin':
        fabricObject = new fabric.Circle({
          left: x,
          top: y,
          radius: 8,
          fill: '#ef4444',
          stroke: '#dc2626',
          strokeWidth: 2
        })
        break
      default:
        fabricObject = new fabric.Rect({
          left: x,
          top: y,
          width: 40,
          height: 40,
          fill: '#e5e7eb',
          stroke: '#374151',
          strokeWidth: 2
        })
    }

    // 添加到画布
    console.log('➕ CustomCanvas Adding object to canvas')
    canvas.add(fabricObject)
    console.log('✅ CustomCanvas Object added to Fabric.js canvas')

    // 尝试设置为活动对象
    try {
      canvas.setActiveObject(fabricObject)
      console.log('✅ CustomCanvas Object set as active')
    } catch (error) {
      console.warn('⚠️ CustomCanvas Could not set active object:', error)
    }

    // 暂时禁用 Fabric.js 渲染以避免 clearRect 错误
    // 几何图形已经成功添加到画布
    console.log('✅ CustomCanvas Skipping Fabric.js render to avoid clearRect error')

    console.log('✅ CustomCanvas Object added, total objects:', canvas.getObjects().length)
  }

  // 键盘事件处理
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // 防止在输入框中触发或设置打开时触发
      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement || isSettingsOpen) {
        return
      }

      if (!fabricCanvasRef.current) return

      const speed = e.shiftKey ? 40 : 20 // Shift 键加速

      // 构建当前按键字符串
      const modifiers = []
      if (e.ctrlKey) modifiers.push('Ctrl')
      if (e.altKey) modifiers.push('Alt')
      if (e.shiftKey) modifiers.push('Shift')
      if (e.metaKey) modifiers.push('Meta')

      let mainKey = e.key
      if (e.key.length === 1) mainKey = e.key.toUpperCase()

      const currentKeyString = modifiers.length > 0
        ? modifiers.join('+') + '+' + mainKey
        : mainKey

      // 获取当前快捷键配置
      const moveUpKey = shortcuts.moveUp || 'W'
      const moveDownKey = shortcuts.moveDown || 'S'
      const moveLeftKey = shortcuts.moveLeft || 'A'
      const moveRightKey = shortcuts.moveRight || 'D'

      console.log('🎹 CustomCanvas Key pressed:', currentKeyString)

      if (currentKeyString === moveUpKey) {
        e.preventDefault()
        dispatch(moveByWASD({ direction: 'w', speed }))
        setViewTransform(prev => ({ ...prev, y: prev.y + speed }))
        console.log('🔼 CustomCanvas Moving up with key:', currentKeyString)
      } else if (currentKeyString === moveLeftKey) {
        e.preventDefault()
        dispatch(moveByWASD({ direction: 'a', speed }))
        setViewTransform(prev => ({ ...prev, x: prev.x + speed }))
        console.log('◀️ CustomCanvas Moving left with key:', currentKeyString)
      } else if (currentKeyString === moveDownKey) {
        e.preventDefault()
        dispatch(moveByWASD({ direction: 's', speed }))
        setViewTransform(prev => ({ ...prev, y: prev.y - speed }))
        console.log('🔽 CustomCanvas Moving down with key:', currentKeyString)
      } else if (currentKeyString === moveRightKey) {
        e.preventDefault()
        dispatch(moveByWASD({ direction: 'd', speed }))
        setViewTransform(prev => ({ ...prev, x: prev.x - speed }))
        console.log('▶️ CustomCanvas Moving right with key:', currentKeyString)
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [dispatch, isInitialized, isSettingsOpen, shortcuts])

  // 鼠标滚轮缩放
  useEffect(() => {
    if (!canvasRef.current) return

    const canvasElement = canvasRef.current

    const handleWheel = (e: WheelEvent) => {
      e.preventDefault()
      e.stopPropagation()

      const delta = e.deltaY > 0 ? 0.9 : 1.1
      const rect = canvasElement.getBoundingClientRect()
      const pointer = {
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
      }

      // 更新本地视图变换
      setViewTransform(prev => ({
        ...prev,
        scale: Math.max(0.1, Math.min(5, prev.scale * delta))
      }))

      // 同步到 Redux store
      dispatch(zoomBy({
        factor: delta,
        center: { x: pointer.x, y: pointer.y }
      }))

      console.log('🔍 CustomCanvas Zoom:', delta > 1 ? 'in' : 'out', 'at', pointer.x, pointer.y)
    }

    canvasElement.addEventListener('wheel', handleWheel, { passive: false })

    return () => {
      canvasElement.removeEventListener('wheel', handleWheel)
    }
  }, [dispatch, isInitialized])

  // 拖放处理函数
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    console.log('🎯 CustomCanvas Drag over canvas container')
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    console.log('🎯 CustomCanvas Drop on canvas container, draggedComponentType:', draggedComponentType)

    if (!draggedComponentType) {
      console.warn('❌ CustomCanvas No dragged component type')
      return
    }

    // 获取容器的位置
    const rect = e.currentTarget.getBoundingClientRect()
    const x = e.clientX - rect.left
    const y = e.clientY - rect.top

    console.log('📍 CustomCanvas Drop position:', { x, y, clientX: e.clientX, clientY: e.clientY, rect })

    // 考虑视图变换，计算实际的画布坐标
    const actualX = (x - viewTransform.x) / viewTransform.scale
    const actualY = (y - viewTransform.y) / viewTransform.scale

    // 网格吸附
    const finalX = snapToGrid ? Math.round(actualX / gridSize) * gridSize : actualX
    const finalY = snapToGrid ? Math.round(actualY / gridSize) * gridSize : actualY

    console.log('📍 CustomCanvas Final position:', { finalX, finalY, actualX, actualY })

    // 创建几何图形
    createShape(draggedComponentType, finalX, finalY)
    dispatch(setDraggingComponent({ isDragging: false }))
  }

  return (
    <div
      className="canvas-container"
      style={{ backgroundColor: '#f9fafb' }}
      onDragOver={handleDragOver}
      onDrop={handleDrop}
    >
      <div
        style={{
          transform: `translate(${viewTransform.x}px, ${viewTransform.y}px) scale(${viewTransform.scale})`,
          transformOrigin: '0 0',
          transition: 'transform 0.1s ease-out'
        }}
      >
        <canvas
          ref={canvasRef}
          className="canvas"
          style={{
            cursor: isDraggingComponent ? 'copy' : 'default'
          }}
        />
      </div>

      {/* 画布信息显示 */}
      <div className="absolute top-4 right-4 bg-white bg-opacity-90 px-3 py-2 rounded-md text-sm text-gray-600">
        <div>缩放: {Math.round(viewTransform.scale * 100)}%</div>
        <div>位置: ({Math.round(viewTransform.x)}, {Math.round(viewTransform.y)})</div>
        <div>网格: {gridVisible ? '显示' : '隐藏'} ({gridSize}px)</div>
        <div className="text-xs text-gray-500 mt-1">
          自定义元件设计模式
        </div>
      </div>
    </div>
  )
}

export default CustomCanvas
