import React from 'react'
import { useAppDispatch, useAppSelector } from '../store/hooks'
import { setDraggingComponent } from '../store/slices/uiSlice'

interface SidebarProps {
  activePanel: 'canvas' | 'custom'
}

// 基础元件库
const basicComponents = [
  { id: 'resistor', name: '电阻', icon: '🔌', description: '基础电阻元件' },
  { id: 'capacitor', name: '电容', icon: '⚡', description: '基础电容元件' },
  { id: 'inductor', name: '电感', icon: '🌀', description: '基础电感元件' },
  { id: 'diode', name: '二极管', icon: '🔺', description: '单向导通元件' },
  { id: 'transistor', name: '三极管', icon: '🔻', description: '放大/开关元件' },
  { id: 'switch', name: '开关', icon: '🔘', description: '手动开关' },
  { id: 'battery', name: '电池', icon: '🔋', description: '直流电源' },
  { id: 'ground', name: '接地', icon: '⏚', description: '接地符号' }
]

// 几何图形工具（自定义面板用）
const geometryTools = [
  { id: 'rectangle', name: '矩形', icon: '⬜', description: '绘制矩形' },
  { id: 'circle', name: '圆形', icon: '⭕', description: '绘制圆形' },
  { id: 'triangle', name: '三角形', icon: '🔺', description: '绘制三角形' },
  { id: 'line', name: '直线', icon: '📏', description: '绘制直线' },
  { id: 'text', name: '文本', icon: '📝', description: '添加文本' },
  { id: 'pin', name: '引脚', icon: '📍', description: '添加引脚' }
]

const Sidebar: React.FC<SidebarProps> = ({ activePanel }) => {
  const dispatch = useAppDispatch()
  const sidebarVisible = useAppSelector(state => state.ui.sidebarVisible)

  const handleDragStart = (e: React.DragEvent, componentType: string) => {
    console.log('🚀 Drag start:', componentType)
    e.dataTransfer.setData('text/plain', componentType)
    e.dataTransfer.effectAllowed = 'copy'
    dispatch(setDraggingComponent({ isDragging: true, componentType }))
  }

  const handleDragEnd = () => {
    console.log('🏁 Drag end')
    dispatch(setDraggingComponent({ isDragging: false }))
  }

  if (!sidebarVisible) {
    return null
  }

  return (
    <div className="sidebar">
      {activePanel === 'canvas' ? (
        <>
          {/* 基础元件库 */}
          <div className="sidebar-section">
            <h3 className="sidebar-title">基础元件</h3>
            <div className="grid grid-cols-2 gap-2">
              {basicComponents.map(component => (
                <div
                  key={component.id}
                  className="component-item"
                  draggable
                  onDragStart={(e) => handleDragStart(e, component.id)}
                  onDragEnd={handleDragEnd}
                  title={component.description}
                >
                  <div className="text-center">
                    <div className="text-2xl mb-1">{component.icon}</div>
                    <div className="text-xs font-medium">{component.name}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 自定义元件库 */}
          <div className="sidebar-section">
            <h3 className="sidebar-title">自定义元件</h3>
            <div className="text-center text-gray-500 text-sm py-4">
              暂无自定义元件
              <br />
              <button className="text-primary-600 hover:text-primary-700 mt-2">
                + 创建新元件
              </button>
            </div>
          </div>

          {/* 属性面板 */}
          <div className="sidebar-section flex-1">
            <h3 className="sidebar-title">属性</h3>
            <div className="text-center text-gray-500 text-sm py-4">
              选择元件查看属性
            </div>
          </div>
        </>
      ) : (
        <>
          {/* 几何图形工具 */}
          <div className="sidebar-section">
            <h3 className="sidebar-title">绘图工具</h3>
            <div className="grid grid-cols-2 gap-2">
              {geometryTools.map(tool => (
                <div
                  key={tool.id}
                  className="component-item"
                  draggable
                  onDragStart={(e) => handleDragStart(e, tool.id)}
                  onDragEnd={handleDragEnd}
                  title={tool.description}
                >
                  <div className="text-center">
                    <div className="text-2xl mb-1">{tool.icon}</div>
                    <div className="text-xs font-medium">{tool.name}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 元件属性设置 */}
          <div className="sidebar-section">
            <h3 className="sidebar-title">元件设置</h3>
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  元件名称
                </label>
                <input
                  type="text"
                  className="w-full px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
                  placeholder="输入元件名称"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  描述
                </label>
                <textarea
                  className="w-full px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"
                  rows={3}
                  placeholder="输入元件描述"
                />
              </div>
              <button className="w-full bg-primary-600 text-white py-2 px-4 rounded-md text-sm font-medium hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500">
                保存元件
              </button>
            </div>
          </div>

          {/* 已保存的自定义元件 */}
          <div className="sidebar-section flex-1">
            <h3 className="sidebar-title">已保存元件</h3>
            <div className="text-center text-gray-500 text-sm py-4">
              暂无已保存的元件
            </div>
          </div>
        </>
      )}
    </div>
  )
}

export default Sidebar
