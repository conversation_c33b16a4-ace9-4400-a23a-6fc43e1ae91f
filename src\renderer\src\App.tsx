import React, { useState } from 'react'
import Toolbar from './components/Toolbar'
import Sidebar from './components/Sidebar'
import SimpleCanvas from './components/SimpleCanvas'
import SimpleCustomCanvas from './components/SimpleCustomCanvas'
import { useAppSelector } from './store/hooks'

function App() {
  const [activePanel, setActivePanel] = useState<'canvas' | 'custom'>('canvas')
  const [isSettingsOpen, setIsSettingsOpen] = useState(false)
  const currentCircuit = useAppSelector(state => state.circuit.current)
  const projectState = useAppSelector(state => state.project)

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* 标题栏 */}
      <div className="bg-gray-800 text-white px-4 py-2 flex items-center justify-between">
        <div className="flex items-center gap-3">
          <span className="text-lg font-semibold">⚡ 元件工坊</span>
          <span className="text-gray-300">|</span>
          <span className="text-gray-200">
            {projectState.projectName}
            {projectState.isModified && <span className="text-orange-400 ml-1">●</span>}
          </span>
        </div>
        <div className="text-sm text-gray-400">
          {projectState.lastSaved ? (
            `最后保存: ${new Date(projectState.lastSaved).toLocaleTimeString()}`
          ) : (
            '未保存'
          )}
        </div>
      </div>

      {/* 工具栏 */}
      <Toolbar
        activePanel={activePanel}
        onPanelChange={setActivePanel}
        onSettingsStateChange={setIsSettingsOpen}
      />
      
      <div className="flex-1 flex">
        {/* 侧边栏 */}
        <Sidebar activePanel={activePanel} />
        
        {/* 主内容区域 */}
        <div className="flex-1 flex flex-col">
          {activePanel === 'canvas' ? (
            <SimpleCanvas isSettingsOpen={isSettingsOpen} />
          ) : (
            <SimpleCustomCanvas isSettingsOpen={isSettingsOpen} />
          )}
        </div>
      </div>
      
      {/* 状态栏 */}
      <div className="bg-white border-t border-gray-200 px-4 py-1 text-xs text-gray-600 flex items-center justify-between">
        <div>
          {currentCircuit ? `项目: ${currentCircuit.name}` : '未打开项目'}
        </div>
        <div>
          元件工坊 v1.0.0
        </div>
      </div>
    </div>
  )
}

export default App
