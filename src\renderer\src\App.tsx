import React, { useState } from 'react'
import Toolbar from './components/Toolbar'
import Sidebar from './components/Sidebar'
import SimpleCanvas from './components/SimpleCanvas'
import SimpleCustomCanvas from './components/SimpleCustomCanvas'
import { useAppSelector } from './store/hooks'

function App() {
  const [activePanel, setActivePanel] = useState<'canvas' | 'custom'>('canvas')
  const [isSettingsOpen, setIsSettingsOpen] = useState(false)
  const currentCircuit = useAppSelector(state => state.circuit.current)

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* 工具栏 */}
      <Toolbar
        activePanel={activePanel}
        onPanelChange={setActivePanel}
        onSettingsStateChange={setIsSettingsOpen}
      />
      
      <div className="flex-1 flex">
        {/* 侧边栏 */}
        <Sidebar activePanel={activePanel} />
        
        {/* 主内容区域 */}
        <div className="flex-1 flex flex-col">
          {activePanel === 'canvas' ? (
            <SimpleCanvas isSettingsOpen={isSettingsOpen} />
          ) : (
            <SimpleCustomCanvas isSettingsOpen={isSettingsOpen} />
          )}
        </div>
      </div>
      
      {/* 状态栏 */}
      <div className="bg-white border-t border-gray-200 px-4 py-1 text-xs text-gray-600 flex items-center justify-between">
        <div>
          {currentCircuit ? `项目: ${currentCircuit.name}` : '未打开项目'}
        </div>
        <div>
          元件工坊 v1.0.0
        </div>
      </div>
    </div>
  )
}

export default App
