import React, { useState, useCallback } from 'react'
import Toolbar from './components/Toolbar'
import Sidebar from './components/Sidebar'
import Canvas from './components/Canvas'
import CustomCanvas from './components/CustomCanvas'
import { useAppSelector } from './store/hooks'
import { useAutoSave } from './hooks/useAutoSave'

function App() {
  const [activePanel, setActivePanel] = useState<'canvas' | 'custom'>('canvas')
  const [isSettingsOpen, setIsSettingsOpen] = useState(false)
  const currentCircuit = useAppSelector(state => state.circuit.current)

  // 自动保存回调
  const handleAutoSave = useCallback(() => {
    // 这里可以实现实际的保存逻辑
    // 比如保存到 localStorage 或发送到服务器
    const projectData = {
      circuit: currentCircuit,
      timestamp: new Date().toISOString(),
      activePanel
    }

    try {
      localStorage.setItem('ecraft_autosave', JSON.stringify(projectData))
      console.log('✅ 项目已自动保存到本地存储')
    } catch (error) {
      console.error('❌ 自动保存失败:', error)
    }
  }, [currentCircuit, activePanel])

  // 使用自动保存 hook
  const { markSaved } = useAutoSave(handleAutoSave)

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* 工具栏 */}
      <Toolbar
        activePanel={activePanel}
        onPanelChange={setActivePanel}
        onSettingsStateChange={setIsSettingsOpen}
      />
      
      <div className="flex-1 flex">
        {/* 侧边栏 */}
        <Sidebar activePanel={activePanel} />
        
        {/* 主内容区域 */}
        <div className="flex-1 flex flex-col">
          {activePanel === 'canvas' ? (
            <Canvas isSettingsOpen={isSettingsOpen} />
          ) : (
            <CustomCanvas isSettingsOpen={isSettingsOpen} />
          )}
        </div>
      </div>
      
      {/* 状态栏 */}
      <div className="bg-white border-t border-gray-200 px-4 py-1 text-xs text-gray-600 flex items-center justify-between">
        <div>
          {currentCircuit ? `项目: ${currentCircuit.name}` : '未打开项目'}
        </div>
        <div>
          元件工坊 v1.0.0
        </div>
      </div>
    </div>
  )
}

export default App
