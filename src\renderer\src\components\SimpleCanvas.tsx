import React, { useRef, useEffect, useState } from 'react'
import { fabric } from 'fabric'
import { useAppDispatch, useAppSelector } from '../store/hooks'
import { addComponent, moveByWASD } from '../store/slices/circuitSlice'

interface SimpleCanvasProps {
  isSettingsOpen?: boolean
}

const SimpleCanvas: React.FC<SimpleCanvasProps> = ({ isSettingsOpen = false }) => {
  const dispatch = useAppDispatch()
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const fabricCanvasRef = useRef<fabric.Canvas | null>(null)
  const [isInitialized, setIsInitialized] = useState(false)
  const [viewTransform, setViewTransform] = useState({ x: 0, y: 0, scale: 1 })

  // Redux 状态
  const components = useAppSelector(state => state.circuit.components)
  const shortcuts = useAppSelector(state => state.ui.shortcuts)
  const { isDraggingComponent, draggedComponentType } = useAppSelector(state => state.ui)
  const { gridVisible, gridSize, snapToGrid } = useAppSelector(state => state.ui)

  // 简化的网格绘制函数
  const drawGrid = () => {
    if (!canvasRef.current) return
    
    const ctx = canvasRef.current.getContext('2d')
    if (!ctx) return

    const canvasWidth = canvasRef.current.width
    const canvasHeight = canvasRef.current.height

    // 清除画布
    ctx.clearRect(0, 0, canvasWidth, canvasHeight)
    
    // 绘制背景
    ctx.fillStyle = '#f9fafb'
    ctx.fillRect(0, 0, canvasWidth, canvasHeight)

    // 绘制网格
    if (gridVisible) {
      ctx.strokeStyle = '#e5e7eb'
      ctx.lineWidth = 1
      ctx.globalAlpha = 0.5

      // 绘制垂直线
      for (let x = 0; x <= canvasWidth; x += gridSize) {
        ctx.beginPath()
        ctx.moveTo(x, 0)
        ctx.lineTo(x, canvasHeight)
        ctx.stroke()
      }

      // 绘制水平线
      for (let y = 0; y <= canvasHeight; y += gridSize) {
        ctx.beginPath()
        ctx.moveTo(0, y)
        ctx.lineTo(canvasWidth, y)
        ctx.stroke()
      }

      ctx.globalAlpha = 1
    }
  }

  // 初始化 Fabric.js Canvas
  useEffect(() => {
    if (!canvasRef.current || isInitialized) return

    const canvas = new fabric.Canvas(canvasRef.current, {
      width: (window.innerWidth - 256) * 2,
      height: (window.innerHeight - 100) * 2,
      backgroundColor: 'transparent'
    })

    fabricCanvasRef.current = canvas
    setIsInitialized(true)

    // 初始绘制网格
    setTimeout(drawGrid, 100)

    // 处理窗口大小变化
    const handleResize = () => {
      canvas.setDimensions({
        width: (window.innerWidth - 256) * 2,
        height: (window.innerHeight - 100) * 2
      })
      drawGrid()
    }

    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
      canvas.dispose()
    }
  }, [isInitialized])

  // 监听网格状态变化
  useEffect(() => {
    if (isInitialized) {
      drawGrid()
    }
  }, [gridVisible, gridSize, isInitialized])

  // 键盘事件处理
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement || isSettingsOpen) {
        return
      }

      const speed = e.shiftKey ? 40 : 20

      // 构建当前按键字符串
      const modifiers = []
      if (e.ctrlKey) modifiers.push('Ctrl')
      if (e.altKey) modifiers.push('Alt')
      if (e.shiftKey) modifiers.push('Shift')
      if (e.metaKey) modifiers.push('Meta')
      
      let mainKey = e.key
      if (e.key.length === 1) mainKey = e.key.toUpperCase()
      
      const currentKeyString = modifiers.length > 0 
        ? modifiers.join('+') + '+' + mainKey 
        : mainKey
      
      // 获取当前快捷键配置并转换为大写进行比较
      const moveUpKey = (shortcuts.moveUp || 'w').toUpperCase()
      const moveDownKey = (shortcuts.moveDown || 's').toUpperCase()
      const moveLeftKey = (shortcuts.moveLeft || 'a').toUpperCase()
      const moveRightKey = (shortcuts.moveRight || 'd').toUpperCase()
      
      if (currentKeyString === moveUpKey) {
        e.preventDefault()
        dispatch(moveByWASD({ direction: 'w', speed }))
        setViewTransform(prev => ({ ...prev, y: prev.y + speed }))
      } else if (currentKeyString === moveLeftKey) {
        e.preventDefault()
        dispatch(moveByWASD({ direction: 'a', speed }))
        setViewTransform(prev => ({ ...prev, x: prev.x + speed }))
      } else if (currentKeyString === moveDownKey) {
        e.preventDefault()
        dispatch(moveByWASD({ direction: 's', speed }))
        setViewTransform(prev => ({ ...prev, y: prev.y - speed }))
      } else if (currentKeyString === moveRightKey) {
        e.preventDefault()
        dispatch(moveByWASD({ direction: 'd', speed }))
        setViewTransform(prev => ({ ...prev, x: prev.x - speed }))
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [dispatch, isInitialized, isSettingsOpen, shortcuts])

  // 鼠标滚轮缩放
  useEffect(() => {
    if (!canvasRef.current) return

    const handleWheel = (e: WheelEvent) => {
      e.preventDefault()
      const delta = e.deltaY > 0 ? 0.9 : 1.1
      setViewTransform(prev => ({
        ...prev,
        scale: Math.max(0.1, Math.min(5, prev.scale * delta))
      }))
    }

    canvasRef.current.addEventListener('wheel', handleWheel)
    return () => {
      if (canvasRef.current) {
        canvasRef.current.removeEventListener('wheel', handleWheel)
      }
    }
  }, [])

  // 处理拖放
  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    
    if (!isDraggingComponent || !draggedComponentType) return

    const rect = canvasRef.current?.getBoundingClientRect()
    if (!rect) return

    const x = e.clientX - rect.left
    const y = e.clientY - rect.top

    // 网格吸附
    const finalX = snapToGrid ? Math.round(x / gridSize) * gridSize : x
    const finalY = snapToGrid ? Math.round(y / gridSize) * gridSize : y

    const component = {
      id: `${draggedComponentType}-${Date.now()}`,
      type: draggedComponentType,
      position: { x: finalX, y: finalY },
      properties: {},
      connections: []
    }

    dispatch(addComponent(component))
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
  }

  return (
    <div className="flex-1 relative overflow-hidden bg-gray-50">
      <canvas
        ref={canvasRef}
        className="absolute inset-0 cursor-crosshair"
        style={{
          transform: `translate(${viewTransform.x}px, ${viewTransform.y}px) scale(${viewTransform.scale})`,
          transformOrigin: '0 0'
        }}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
      />
      
      {/* 画布信息显示 */}
      <div className="absolute top-4 right-4 bg-white bg-opacity-90 px-3 py-2 rounded-md text-sm text-gray-600">
        <div>缩放: {Math.round(viewTransform.scale * 100)}%</div>
        <div>位置: ({Math.round(viewTransform.x)}, {Math.round(viewTransform.y)})</div>
        <div>网格: {gridVisible ? '显示' : '隐藏'} ({gridSize}px)</div>
        <div className="text-xs text-gray-500 mt-1">
          WASD: 移动视图 | 滚轮: 缩放
        </div>
      </div>
    </div>
  )
}

export default SimpleCanvas
