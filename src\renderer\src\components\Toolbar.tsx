import React, { useState } from 'react'
import { useAppDispatch, useAppSelector } from '../store/hooks'
import { toggleGrid } from '../store/slices/uiSlice'
import Settings from './Settings'

interface ToolbarProps {
  activePanel: 'canvas' | 'custom'
  onPanelChange: (panel: 'canvas' | 'custom') => void
  onSettingsStateChange?: (isOpen: boolean) => void
}

const Toolbar: React.FC<ToolbarProps> = ({ activePanel, onPanelChange, onSettingsStateChange }) => {
  const dispatch = useAppDispatch()
  const gridVisible = useAppSelector(state => state.ui.gridVisible)
  const [showSettings, setShowSettings] = useState(false)

  const handleSettingsToggle = (isOpen: boolean) => {
    setShowSettings(isOpen)
    onSettingsStateChange?.(isOpen)
  }

  return (
    <>
      <div className="toolbar">
        {/* 面板切换 */}
        <div className="flex items-center gap-2 border-r border-gray-300 pr-4">
          <button
            className={`toolbar-button ${activePanel === 'canvas' ? 'active' : ''}`}
            onClick={() => onPanelChange('canvas')}
            title="画板面板"
          >
            🎨 画板
          </button>
          <button
            className={`toolbar-button ${activePanel === 'custom' ? 'active' : ''}`}
            onClick={() => onPanelChange('custom')}
            title="自定义面板"
          >
            🔧 自定义
          </button>
        </div>

        {/* 网格切换 */}
        <div className="flex items-center gap-2 border-r border-gray-300 px-4">
          <button
            className={`toolbar-button ${gridVisible ? 'active' : ''}`}
            onClick={() => dispatch(toggleGrid())}
            title="切换网格显示"
          >
            ⊞ 网格
          </button>
        </div>

        {/* 右侧空间 */}
        <div className="flex-1"></div>

        {/* 设置按钮 */}
        <div className="flex items-center gap-2 pl-4">
          <button
            className="toolbar-button"
            onClick={() => handleSettingsToggle(true)}
            title="设置"
          >
            ⚙️ 设置
          </button>
        </div>
      </div>

      {/* 设置弹窗 */}
      <Settings
        isOpen={showSettings}
        onClose={() => handleSettingsToggle(false)}
      />
    </>
  )
}

export default Toolbar
