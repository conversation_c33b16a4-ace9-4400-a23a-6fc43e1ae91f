import { configureStore } from '@reduxjs/toolkit'
import circuitReducer from './slices/circuitSlice'
import viewportReducer from './slices/viewportSlice'
import uiReducer from './slices/uiSlice'
import projectReducer from './slices/projectSlice'

export const store = configureStore({
  reducer: {
    circuit: circuitReducer,
    viewport: viewportReducer,
    ui: uiReducer,
    project: projectReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        // 忽略这些 action types 的序列化检查
        ignoredActions: ['persist/PERSIST'],
      },
    }),
})

export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch
