{"name": "ecraft", "version": "1.0.0", "description": "元件工坊 - 简洁易用的电路设计软件", "main": "dist/main/main.js", "author": "Your Name", "license": "MIT", "private": true, "scripts": {"dev": "concurrently \"npm run dev:renderer\" \"npm run dev:main\"", "dev:renderer": "vite", "dev:main": "npm run build:main && electron dist/main/main.js", "build": "npm run build:renderer && npm run build:main", "build:renderer": "vite build", "build:main": "tsc -p tsconfig.main.json", "pack": "electron-builder", "dist": "npm run build && electron-builder", "postinstall": "electron-builder install-app-deps"}, "dependencies": {"@reduxjs/toolkit": "^1.9.7", "fabric": "^5.3.0", "mousetrap": "^1.6.5", "react": "^18.2.0", "react-dom": "^18.2.0", "react-redux": "^8.1.3"}, "devDependencies": {"@headlessui/react": "^1.7.17", "@types/fabric": "^5.3.10", "@types/mousetrap": "^1.6.11", "@types/node": "^20.8.0", "@types/react": "^18.2.31", "@types/react-dom": "^18.2.14", "@vitejs/plugin-react": "^4.1.0", "autoprefixer": "^10.4.16", "concurrently": "^8.2.2", "electron": "^27.0.0", "electron-builder": "^24.6.4", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^4.4.11"}, "build": {"appId": "com.ecraft.app", "productName": "元件工坊", "directories": {"output": "release"}, "files": ["dist/**/*", "assets/**/*"], "mac": {"category": "public.app-category.productivity"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}}