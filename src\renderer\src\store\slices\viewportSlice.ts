import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import { Point } from '../../../shared/utils'

interface ViewportState {
  x: number
  y: number
  zoom: number
  isDragging: boolean
  isAnimating: boolean
  targetPosition?: Point
  targetZoom?: number
}

const initialState: ViewportState = {
  x: 0,
  y: 0,
  zoom: 1,
  isDragging: false,
  isAnimating: false
}

const viewportSlice = createSlice({
  name: 'viewport',
  initialState,
  reducers: {
    // 视图平移
    setPosition: (state, action: PayloadAction<Point>) => {
      state.x = action.payload.x
      state.y = action.payload.y
    },

    moveBy: (state, action: PayloadAction<Point>) => {
      state.x += action.payload.x
      state.y += action.payload.y
    },

    // 平滑移动（模仿图灵完备的效果）
    animateToPosition: (state, action: PayloadAction<Point>) => {
      state.targetPosition = action.payload
      state.isAnimating = true
    },

    // WASD 键移动
    moveByWASD: (state, action: PayloadAction<{ direction: 'w' | 'a' | 's' | 'd'; speed?: number }>) => {
      const speed = action.payload.speed || 20
      const moveAmount = speed / state.zoom // 根据缩放调整移动速度
      
      switch (action.payload.direction) {
        case 'w': // 上
          state.y -= moveAmount
          break
        case 's': // 下
          state.y += moveAmount
          break
        case 'a': // 左
          state.x -= moveAmount
          break
        case 'd': // 右
          state.x += moveAmount
          break
      }
    },

    // 缩放
    setZoom: (state, action: PayloadAction<number>) => {
      state.zoom = Math.max(0.1, Math.min(5, action.payload))
    },

    zoomBy: (state, action: PayloadAction<{ factor: number; center?: Point }>) => {
      const oldZoom = state.zoom
      const newZoom = Math.max(0.1, Math.min(5, oldZoom * action.payload.factor))
      
      if (action.payload.center) {
        // 以指定点为中心缩放
        const center = action.payload.center
        const zoomRatio = newZoom / oldZoom
        
        state.x = center.x - (center.x - state.x) * zoomRatio
        state.y = center.y - (center.y - state.y) * zoomRatio
      }
      
      state.zoom = newZoom
    },

    // 平滑缩放
    animateToZoom: (state, action: PayloadAction<{ zoom: number; center?: Point }>) => {
      state.targetZoom = Math.max(0.1, Math.min(5, action.payload.zoom))
      state.isAnimating = true
      
      if (action.payload.center) {
        const center = action.payload.center
        const zoomRatio = state.targetZoom / state.zoom
        state.targetPosition = {
          x: center.x - (center.x - state.x) * zoomRatio,
          y: center.y - (center.y - state.y) * zoomRatio
        }
      }
    },

    // 拖拽状态
    setDragging: (state, action: PayloadAction<boolean>) => {
      state.isDragging = action.payload
    },

    // 动画状态
    setAnimating: (state, action: PayloadAction<boolean>) => {
      state.isAnimating = action.payload
      if (!action.payload) {
        state.targetPosition = undefined
        state.targetZoom = undefined
      }
    },

    // 完成动画步骤
    animationStep: (state, action: PayloadAction<{ position?: Point; zoom?: number }>) => {
      if (action.payload.position) {
        state.x = action.payload.position.x
        state.y = action.payload.position.y
      }
      if (action.payload.zoom) {
        state.zoom = action.payload.zoom
      }
    },

    // 重置视图
    resetView: (state) => {
      state.x = 0
      state.y = 0
      state.zoom = 1
      state.isDragging = false
      state.isAnimating = false
      state.targetPosition = undefined
      state.targetZoom = undefined
    },

    // 加载视图状态
    loadViewport: (state, action: PayloadAction<Partial<ViewportState>>) => {
      const { x, y, zoom } = action.payload
      if (x !== undefined) state.x = x
      if (y !== undefined) state.y = y
      if (zoom !== undefined) state.zoom = Math.max(0.1, Math.min(5, zoom))
      state.isDragging = false
      state.isAnimating = false
      state.targetPosition = undefined
      state.targetZoom = undefined
    },

    // 适应内容
    fitToContent: (state, action: PayloadAction<{ bounds: { x: number; y: number; width: number; height: number }; canvasSize: { width: number; height: number } }>) => {
      const { bounds, canvasSize } = action.payload
      const padding = 50
      
      const scaleX = (canvasSize.width - padding * 2) / bounds.width
      const scaleY = (canvasSize.height - padding * 2) / bounds.height
      const scale = Math.min(scaleX, scaleY, 1) // 不放大，只缩小
      
      state.zoom = scale
      state.x = (canvasSize.width - bounds.width * scale) / 2 - bounds.x * scale
      state.y = (canvasSize.height - bounds.height * scale) / 2 - bounds.y * scale
    }
  }
})

export const {
  setPosition,
  moveBy,
  animateToPosition,
  moveByWASD,
  setZoom,
  zoomBy,
  animateToZoom,
  setDragging,
  setAnimating,
  animationStep,
  resetView,
  loadViewport,
  fitToContent
} = viewportSlice.actions

export default viewportSlice.reducer
