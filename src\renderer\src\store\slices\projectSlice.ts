import { createSlice, PayloadAction } from '@reduxjs/toolkit'

interface ProjectState {
  currentFile: string | null // 当前文件路径
  isModified: boolean // 是否有未保存的修改
  projectName: string // 项目名称
  lastSaved: number | null // 最后保存时间戳
}

const initialState: ProjectState = {
  currentFile: null,
  isModified: false,
  projectName: '未命名项目',
  lastSaved: null
}

const projectSlice = createSlice({
  name: 'project',
  initialState,
  reducers: {
    // 新建项目
    newProject: (state) => {
      state.currentFile = null
      state.isModified = false
      state.projectName = '未命名项目'
      state.lastSaved = null
    },

    // 打开项目
    openProject: (state, action: PayloadAction<{ filePath: string; projectName: string }>) => {
      state.currentFile = action.payload.filePath
      state.projectName = action.payload.projectName
      state.isModified = false
      state.lastSaved = Date.now()
    },

    // 保存项目
    saveProject: (state, action: PayloadAction<{ filePath?: string }>) => {
      if (action.payload.filePath) {
        state.currentFile = action.payload.filePath
        // 从文件路径提取项目名称
        const fileName = action.payload.filePath.split(/[/\\]/).pop() || '未命名项目'
        state.projectName = fileName.replace(/\.[^/.]+$/, '') // 移除扩展名
      }
      state.isModified = false
      state.lastSaved = Date.now()
    },

    // 标记为已修改
    markAsModified: (state) => {
      state.isModified = true
    },

    // 设置项目名称
    setProjectName: (state, action: PayloadAction<string>) => {
      state.projectName = action.payload
      state.isModified = true
    }
  }
})

export const {
  newProject,
  openProject,
  saveProject,
  markAsModified,
  setProjectName
} = projectSlice.actions

export default projectSlice.reducer
