// 文件操作工具函数

export interface ProjectData {
  version: string
  projectName: string
  createdAt: string
  lastModified: string
  circuit: any // 电路数据
  customComponents: any[] // 自定义元件
  viewport: any // 视图状态
}

// 创建新项目数据
export const createNewProjectData = (circuitState: any, customComponents: any[], viewportState: any): ProjectData => {
  return {
    version: '1.0.0',
    projectName: '未命名项目',
    createdAt: new Date().toISOString(),
    lastModified: new Date().toISOString(),
    circuit: circuitState,
    customComponents: customComponents,
    viewport: viewportState
  }
}

// 保存项目到文件
export const saveProjectToFile = async (projectData: ProjectData, suggestedName?: string): Promise<string | null> => {
  try {
    // 创建文件内容
    const fileContent = JSON.stringify(projectData, null, 2)
    
    // 创建下载链接
    const blob = new Blob([fileContent], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    
    // 创建下载元素
    const a = document.createElement('a')
    a.href = url
    a.download = `${suggestedName || projectData.projectName || '未命名项目'}.ecraft`
    
    // 触发下载
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    
    // 清理 URL
    URL.revokeObjectURL(url)
    
    console.log('✅ 项目已保存')
    return a.download
  } catch (error) {
    console.error('❌ 保存项目失败:', error)
    return null
  }
}

// 从文件加载项目
export const loadProjectFromFile = (): Promise<{ data: ProjectData; fileName: string } | null> => {
  return new Promise((resolve) => {
    // 创建文件输入元素
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = '.ecraft,.json'
    
    input.onchange = (event) => {
      const file = (event.target as HTMLInputElement).files?.[0]
      if (!file) {
        resolve(null)
        return
      }
      
      const reader = new FileReader()
      reader.onload = (e) => {
        try {
          const content = e.target?.result as string
          const data = JSON.parse(content) as ProjectData
          
          // 验证文件格式
          if (!data.version || !data.circuit) {
            throw new Error('无效的项目文件格式')
          }
          
          console.log('✅ 项目已加载:', file.name)
          resolve({
            data,
            fileName: file.name.replace(/\.[^/.]+$/, '') // 移除扩展名
          })
        } catch (error) {
          console.error('❌ 加载项目失败:', error)
          alert('无法加载项目文件，请检查文件格式是否正确。')
          resolve(null)
        }
      }
      
      reader.readAsText(file)
    }
    
    input.oncancel = () => {
      resolve(null)
    }
    
    // 触发文件选择
    input.click()
  })
}

// 导出为其他格式
export const exportProject = async (projectData: ProjectData, format: 'json' | 'svg' | 'png') => {
  try {
    switch (format) {
      case 'json':
        return await saveProjectToFile(projectData, `${projectData.projectName}_export`)
      
      case 'svg':
        // TODO: 实现 SVG 导出
        console.log('SVG 导出功能待实现')
        break
      
      case 'png':
        // TODO: 实现 PNG 导出
        console.log('PNG 导出功能待实现')
        break
    }
  } catch (error) {
    console.error('❌ 导出失败:', error)
  }
}

// 检查是否有未保存的更改
export const hasUnsavedChanges = (isModified: boolean): boolean => {
  return isModified
}

// 确认保存对话框
export const confirmSave = (projectName: string): Promise<'save' | 'discard' | 'cancel'> => {
  return new Promise((resolve) => {
    const result = confirm(
      `项目 "${projectName}" 有未保存的更改。\n\n点击"确定"保存更改，点击"取消"放弃更改。`
    )
    
    if (result) {
      resolve('save')
    } else {
      // 再次确认是否放弃更改
      const discard = confirm('确定要放弃所有未保存的更改吗？')
      resolve(discard ? 'discard' : 'cancel')
    }
  })
}
